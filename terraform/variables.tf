# Variables for Terraform configuration

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "ivent-api"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "eu-central-1"
}

# VPC Configuration
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

# Database Configuration
variable "db_password" {
  description = "Password for the RDS instance"
  type        = string
  sensitive   = true
}

variable "db_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t3.micro"
}

variable "db_allocated_storage" {
  description = "Allocated storage for RDS instance (GB)"
  type        = number
  default     = 20
}

variable "db_backup_retention" {
  description = "Backup retention period in days"
  type        = number
  default     = 7
}

# Application Configuration
variable "app_image" {
  description = "Docker image for the application"
  type        = string
  default     = "ivent-api:latest"
}

variable "app_port" {
  description = "Port on which the application runs"
  type        = number
  default     = 3000
}

variable "app_cpu" {
  description = "CPU units for the application (1024 = 1 vCPU)"
  type        = number
  default     = 512
}

variable "app_memory" {
  description = "Memory for the application (MB)"
  type        = number
  default     = 1024
}

variable "app_count" {
  description = "Number of application instances"
  type        = number
  default     = 1
}

# Security
variable "jwt_secret" {
  description = "JWT secret key"
  type        = string
  sensitive   = true
}

# Monitoring
variable "alert_email" {
  description = "Email address for CloudWatch alerts"
  type        = string
  default     = "<EMAIL>"
}

# Other
variable "mapbox_access_token" {
  description = "Mapbox access token"
  type        = string
  sensitive   = true
}

# Environment-specific defaults
variable "environment_configs" {
  description = "Environment-specific configurations"
  type = map(object({
    db_instance_class    = string
    db_allocated_storage = number
    app_cpu             = number
    app_memory          = number
    app_count           = number
  }))
  default = {
    dev = {
      db_instance_class    = "db.t3.micro"
      db_allocated_storage = 20
      app_cpu             = 256
      app_memory          = 512
      app_count           = 1
    }
    staging = {
      db_instance_class    = "db.t3.small"
      db_allocated_storage = 50
      app_cpu             = 512
      app_memory          = 1024
      app_count           = 1
    }
    prod = {
      db_instance_class    = "db.t3.medium"
      db_allocated_storage = 100
      app_cpu             = 1024
      app_memory          = 2048
      app_count           = 2
    }
  }
}
