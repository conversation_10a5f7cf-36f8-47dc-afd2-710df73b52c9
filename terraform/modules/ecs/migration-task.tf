# ECS Task Definition for Database Migrations
resource "aws_ecs_task_definition" "migration" {
  family                   = "${var.name_prefix}-migration"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "256"
  memory                   = "512"
  execution_role_arn       = aws_iam_role.ecs_task_execution.arn
  task_role_arn            = aws_iam_role.ecs_task.arn

  container_definitions = jsonencode([
    {
      name  = "migration"
      image = var.app_image

      essential = true

      # Default command runs migrations
      command = ["/bin/sh", "-c", "npm run migration:run"]

      # Environment variables
      environment = [
        {
          name  = "NODE_ENV"
          value = var.environment
        },
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "POSTGRES_HOST"
          value = "localhost" # Will be overridden by SSM parameters in production
        },
        {
          name  = "POSTGRES_PORT"
          value = "5432"
        },
        {
          name  = "POSTGRES_USER"
          value = "ivent_admin" # Will be overridden by SSM parameters in production
        },
        {
          name  = "POSTGRES_DB"
          value = "ivent_db" # Will be overridden by SSM parameters in production
        },
        {
          name  = "AWS_REGION"
          value = data.aws_region.current.name
        }
      ]

      # Secrets from AWS Systems Manager Parameter Store
      secrets = [
        {
          name      = "POSTGRES_PASSWORD"
          valueFrom = "/ivent/${var.environment}/db/password"
        }
      ]

      # Logging configuration
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.migration.name
          "awslogs-region"        = data.aws_region.current.name
          "awslogs-stream-prefix" = "migration"
        }
      }

      # Health check (optional, for monitoring)
      healthCheck = {
        command     = ["CMD-SHELL", "echo 'Migration task running'"]
        interval    = 30
        timeout     = 5
        retries     = 3
        startPeriod = 0
      }

      # Resource limits
      memoryReservation = 256

      # Stop timeout
      stopTimeout = 120
    }
  ])

  tags = var.tags
}

# CloudWatch Log Group for Migration Tasks
resource "aws_cloudwatch_log_group" "migration" {
  name              = "/ecs/${var.name_prefix}-migration"
  retention_in_days = 7

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-migration-logs"
  })
}

# CloudWatch Metric Alarm for Migration Failures
resource "aws_cloudwatch_metric_alarm" "migration_failures" {
  alarm_name          = "${var.name_prefix}-migration-failures"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TaskCount"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors migration task failures"
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = "${var.name_prefix}-cluster"
    ServiceName = "${var.name_prefix}-migration"
  }

  tags = var.tags
}

# Output the migration task definition ARN
output "migration_task_definition_arn" {
  description = "ARN of the migration task definition"
  value       = aws_ecs_task_definition.migration.arn
}

output "migration_log_group_name" {
  description = "Name of the migration CloudWatch log group"
  value       = aws_cloudwatch_log_group.migration.name
}
