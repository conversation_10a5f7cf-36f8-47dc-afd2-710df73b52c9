#!/bin/bash

# Script to create S3 buckets for Terraform state
# Usage: ./create-s3.sh <environment>

# Source utility functions
# 1. Exit immediately if a command fails
# 2. Functions to print colored output
source "$(dirname "$0")/../../scripts/utils.sh"

# Check if environment is provided
if [ -z "$1" ]; then
    print_error "Environment is required. Usage: ./setup-secrets.sh <environment>"
    print_error "Available environments: dev, staging, prod"
    exit 1
fi

ENVIRONMENT=$1

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

print_status "Setting up secrets for environment: $ENVIRONMENT"

# Check if AWS CLI is installed and configured
check_aws

# Set backend configuration
BACKEND_KEY="terraform.tfstate"
BACKEND_REGION="eu-central-1"
BACKEND_BUCKET="ivent-terraform-state-${ENVIRONMENT}-${BACKEND_REGION}"

print_status "Checking backend: s3://${BACKEND_BUCKET}/${BACKEND_KEY}"

# Create S3 bucket for Terraform state if it doesn't exist
if ! aws s3 ls "s3://${BACKEND_BUCKET}" >/dev/null 2>&1; then
    print_status "Creating Terraform state bucket: $BACKEND_BUCKET"
    aws s3 mb "s3://${BACKEND_BUCKET}" --region ${BACKEND_REGION}
    
    # Enable versioning
    aws s3api put-bucket-versioning \
        --bucket "$BACKEND_BUCKET" \
        --versioning-configuration Status=Enabled
    
    # Enable server-side encryption
    aws s3api put-bucket-encryption \
        --bucket "$BACKEND_BUCKET" \
        --server-side-encryption-configuration '{
            "Rules": [
                {
                    "ApplyServerSideEncryptionByDefault": {
                        "SSEAlgorithm": "AES256"
                    }
                }
            ]
        }'
    
    print_status "Terraform state bucket created and configured"
else
    print_status "Terraform state bucket already exists: $BACKEND_BUCKET"
fi