# Staging Environment Configuration

environment = "staging"

# VPC Configuration
vpc_cidr = "10.1.0.0/16"

# Database Configuration
db_instance_class    = "db.t3.small"
db_allocated_storage = 50
db_backup_retention  = 7

# Application Configuration
app_cpu    = 512
app_memory = 1024
app_count  = 1
app_port   = 3000

# Note: Sensitive variables like db_password and jwt_secret 
# should be set via environment variables or AWS Systems Manager
# Example:
# export TF_VAR_db_password="your_secure_password"
# export TF_VAR_jwt_secret="your_jwt_secret"
