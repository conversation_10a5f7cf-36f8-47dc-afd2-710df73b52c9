# Production Environment Configuration

environment = "prod"

# VPC Configuration
vpc_cidr = "10.2.0.0/16"

# Database Configuration
db_instance_class    = "db.t3.medium"
db_allocated_storage = 100
db_backup_retention  = 30

# Application Configuration
app_cpu    = 1024
app_memory = 2048
app_count  = 2
app_port   = 3000

# Note: Sensitive variables like db_password and jwt_secret 
# should be set via environment variables or AWS Systems Manager
# Example:
# export TF_VAR_db_password="your_secure_password"
# export TF_VAR_jwt_secret="your_jwt_secret"
