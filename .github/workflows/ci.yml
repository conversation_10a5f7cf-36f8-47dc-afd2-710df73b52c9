name: CI Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      localstack:
        image: localstack/localstack:latest
        env:
          SERVICES: s3,ssm,secretsmanager
          DEBUG: 1
        ports:
          - 4566:4566

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npx tsc --noEmit

      - name: Setup test environment
        run: |
          # Set up test environment variables
          echo "NODE_ENV=test" >> $GITHUB_ENV
          echo "POSTGRES_HOST=localhost" >> $GITHUB_ENV
          echo "POSTGRES_PORT=5432" >> $GITHUB_ENV
          echo "POSTGRES_USER=test_user" >> $GITHUB_ENV
          echo "POSTGRES_PASSWORD=test_password" >> $GITHUB_ENV
          echo "POSTGRES_DB=test_db" >> $GITHUB_ENV
          echo "AWS_ENDPOINT_URL=http://localhost:4566" >> $GITHUB_ENV
          echo "AWS_ACCESS_KEY_ID=test" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=test" >> $GITHUB_ENV
          echo "AWS_REGION=eu-central-1" >> $GITHUB_ENV
          echo "AWS_S3_BUCKET_NAME=test-bucket" >> $GITHUB_ENV
          echo "JWT_SECRET=test-jwt-secret" >> $GITHUB_ENV

      - name: Setup LocalStack S3
        run: |
          # Wait for LocalStack to be ready
          timeout 30 bash -c 'until curl -s http://localhost:4566/_localstack/health; do sleep 1; done'

          # Create test S3 bucket
          aws --endpoint-url=http://localhost:4566 s3 mb s3://test-bucket
        env:
          AWS_ACCESS_KEY_ID: test
          AWS_SECRET_ACCESS_KEY: test
          AWS_DEFAULT_REGION: eu-central-1

      - name: Run unit tests
        run: npm run test
        env:
          NODE_ENV: test
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
          AWS_ENDPOINT_URL: http://localhost:4566
          AWS_ACCESS_KEY_ID: test
          AWS_SECRET_ACCESS_KEY: test
          AWS_REGION: eu-central-1
          AWS_S3_BUCKET_NAME: test-bucket
          JWT_SECRET: test-jwt-secret

      - name: Run e2e tests
        run: npm run test:e2e
        env:
          NODE_ENV: test
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
          AWS_ENDPOINT_URL: http://localhost:4566
          AWS_ACCESS_KEY_ID: test
          AWS_SECRET_ACCESS_KEY: test
          AWS_REGION: eu-central-1
          AWS_S3_BUCKET_NAME: test-bucket
          JWT_SECRET: test-jwt-secret

      - name: Generate test coverage
        run: npm run test:cov

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: ivent-api:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=high
