# ivent-api

A comprehensive NestJS backend application with PostgreSQL database and S3 storage, deployed to AWS using Infrastructure as Code (Terraform) and automated CI/CD pipelines.

## 🏗️ Architecture Overview

This application is built with:

- **Backend**: NestJS (Node.js) with TypeScript
- **Database**: PostgreSQL (AWS RDS)
- **Storage**: AWS S3 for media files
- **Infrastructure**: AWS ECS (Fargate) with Application Load Balancer
- **CI/CD**: GitHub Actions with automated testing and deployment
- **Monitoring**: CloudWatch with custom dashboards and alerts
- **Security**: VPC isolation, encrypted storage, secrets management

## 🚀 Quick Start

### Prerequisites

- AWS CLI v2
- Terraform >= 1.0
- Docker
- Node.js 20+
- Git

### Local Development Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd ivent_api

# Install dependencies
npm install

# Start local development environment
docker-compose up -d

# Setup local S3 (LocalStack)
./scripts/setup-local-s3.sh

# Run the application
npm run start:dev
```

The application will be available at:

- API: http://localhost:3000
- Health Check: http://localhost:3000/health
- API Documentation: http://localhost:3000/api

## 🌐 AWS Deployment

### Quick Deployment

```bash
# 1. Setup AWS credentials
aws configure

# 2. Setup secrets
./scripts/setup-secrets.sh dev

# 3. Deploy infrastructure
cd terraform
./scripts/deploy.sh dev apply

# 4. Test deployment
cd ..
./scripts/test-deployment.sh dev
```

For detailed deployment instructions, see [DEPLOYMENT_GUIDE.md](docs/DEPLOYMENT_GUIDE.md).

## 📁 Project Structure

```
ivent_api/
├── src/                          # Application source code
│   ├── modules/                  # NestJS modules
│   │   ├── aws-s3/              # AWS S3 integration
│   │   ├── vibes/               # Vibes functionality
│   │   └── ...                  # Other modules
│   ├── entities/                # TypeORM entities
│   └── main.ts                  # Application entry point
├── terraform/                   # Infrastructure as Code
│   ├── modules/                 # Terraform modules
│   │   ├── vpc/                 # VPC and networking
│   │   ├── rds/                 # Database
│   │   ├── s3/                  # Storage
│   │   ├── ecs/                 # Container orchestration
│   │   └── monitoring/          # CloudWatch monitoring
│   ├── environments/            # Environment-specific configs
│   └── scripts/                 # Deployment scripts
├── .github/workflows/           # CI/CD pipelines
├── scripts/                     # Utility scripts
├── docs/                        # Documentation
└── docker-compose.yml          # Local development
```

## 🔧 Available Scripts

### Development

```bash
npm run start:dev      # Start development server
npm run build          # Build the application
npm run test           # Run unit tests
npm run test:e2e       # Run end-to-end tests
npm run lint           # Run ESLint
```

### Infrastructure

```bash
./scripts/setup-secrets.sh <env>     # Setup AWS secrets
./terraform/scripts/deploy.sh <env>  # Deploy infrastructure
./scripts/test-deployment.sh <env>   # Test deployment
./scripts/setup-local-s3.sh          # Setup local S3
```

## 🌍 Environments

The application supports three environments:

| Environment | Branch    | Auto-Deploy | Description                 |
| ----------- | --------- | ----------- | --------------------------- |
| Development | `develop` | ✅ Yes      | Development and testing     |
| Staging     | `staging` | ⚠️ Manual   | Pre-production testing      |
| Production  | `main`    | ⚠️ Manual   | Live production environment |

## 🔐 Security Features

- **Network Security**: VPC isolation with private subnets
- **Data Encryption**: At-rest and in-transit encryption
- **Secrets Management**: AWS Systems Manager Parameter Store
- **Container Security**: Non-root user, multi-stage builds
- **Access Control**: IAM roles with least privilege
- **Monitoring**: CloudWatch alarms and dashboards

See [SECURITY.md](docs/SECURITY.md) for detailed security information.

## 📊 Monitoring

### CloudWatch Dashboard

Access your monitoring dashboard:

```bash
# Get dashboard URL
cd terraform
terraform output cloudwatch_dashboard_url
```

### Key Metrics Monitored

- Application CPU and memory usage
- Database performance and connections
- Load balancer response times and error rates
- Custom application metrics

### Alerts

Automated alerts are configured for:

- High CPU/memory usage
- Database connection issues
- Application errors (5XX responses)
- Slow response times

## 🧪 Testing

### Automated Testing

- **Unit Tests**: Jest-based unit tests
- **E2E Tests**: End-to-end API testing
- **Security Scanning**: Snyk vulnerability scanning
- **Load Testing**: Artillery-based performance testing

### Manual Testing

```bash
# Test specific environment
./scripts/test-deployment.sh dev

# Load testing
artillery quick --count 10 --num 100 http://your-alb-dns/health
```

## 🚀 CI/CD Pipeline

### GitHub Actions Workflows

- **CI Pipeline**: Automated testing, linting, security scanning
- **Development Deploy**: Auto-deploy on `develop` branch
- **Staging Deploy**: Manual deployment workflow
- **Production Deploy**: Manual deployment with approval gates

### Deployment Features

- Blue-green deployments
- Automatic rollback on failure
- Health checks and validation
- Slack/email notifications

## 📚 API Documentation

### Swagger/OpenAPI

- **Development**: http://your-dev-alb/api
- **Staging**: http://your-staging-alb/api
- **Production**: Disabled for security

### Key Endpoints

- `GET /health` - Health check endpoint
- `GET /api` - Swagger UI documentation
- `GET /api-json` - OpenAPI JSON schema

## 🛠️ Troubleshooting

### Common Issues

**Service Won't Start**

```bash
# Check ECS service status
aws ecs describe-services --cluster ivent-api-dev-cluster --services ivent-api-dev-service

# Check logs
aws logs tail /ecs/ivent-api-dev --follow
```

**Database Connection Issues**

```bash
# Test connectivity
./scripts/test-deployment.sh dev

# Check security groups
aws ec2 describe-security-groups --filters "Name=group-name,Values=*ivent-api-dev*"
```

**Load Balancer Issues**

```bash
# Check target health
aws elbv2 describe-target-health --target-group-arn <target-group-arn>
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the [MIT License](LICENSE).

## 📞 Support

For support and questions:

- Create an issue in the GitHub repository
- Contact the development team
- Check the [troubleshooting guide](docs/DEPLOYMENT_GUIDE.md#troubleshooting)

## 🔗 Additional Resources

- [AWS Documentation](https://docs.aws.amazon.com/)
- [NestJS Documentation](https://docs.nestjs.com/)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
