{"compilerOptions": {"moduleResolution": "NodeNext", "module": "NodeNext", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false, "plugins": [{"name": "@nestjs/swagger/plugin"}]}}