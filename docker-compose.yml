version: "3.9"

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ivent_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ivent_dev}
      POSTGRES_USER: ${POSTGRES_USER:-ivent_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-dev_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql  # Temporarily disabled due to encoding issues
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-ivent_user} -d ${POSTGRES_DB:-ivent_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ivent_network

  # LocalStack for AWS services simulation
  localstack:
    image: localstack/localstack:latest
    container_name: ivent_localstack
    environment:
      - SERVICES=s3,ssm,secretsmanager
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    ports:
      - "4566:4566"
    volumes:
      - localstack_data:/tmp/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - ivent_network

  # Application
  backend:
    container_name: ivent_api
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    env_file:
      - .env
    environment:
      # Override for local development
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      # LocalStack endpoints
      AWS_ENDPOINT_URL: http://localstack:4566
      AWS_ACCESS_KEY_ID: test
      AWS_SECRET_ACCESS_KEY: test
      AWS_DEFAULT_REGION: eu-central-1
    ports:
      - "${SERVER_TCP_PORT:-3000}:3000"
    depends_on:
      postgres:
        condition: service_healthy
      localstack:
        condition: service_started
    volumes:
      # Mount SSL certificate if needed
      - ./ca.pem:/app/certs/ca.pem:ro
    networks:
      - ivent_network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
    driver: local
  localstack_data:
    driver: local

networks:
  ivent_network:
    driver: bridge
