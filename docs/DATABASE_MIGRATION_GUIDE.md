# 🗄️ Database Migration Strategy Guide

This guide provides a comprehensive database migration strategy for the ivent-api NestJS application using TypeORM, PostgreSQL, and AWS ECS deployment.

## 📋 Table of Contents

- [Overview](#-overview)
- [Migration Tools Setup](#-migration-tools-setup)
- [Migration Scripts](#-migration-scripts)
- [ECS Integration](#-ecs-integration)
- [Deployment Workflow](#-deployment-workflow)
- [Best Practices](#-best-practices)
- [Troubleshooting](#-troubleshooting)

## 🎯 Overview

### Current Setup Analysis

- **Framework**: NestJS with TypeORM
- **Database**: PostgreSQL 15.12 with PostGIS extension
- **Infrastructure**: AWS RDS + ECS Fargate
- **Region**: eu-central-1
- **Migration Config**: `migrationsRun: true` (auto-run on startup)

### Migration Strategy

1. **TypeORM CLI-based migrations** for schema changes
2. **Separate ECS task** for running migrations
3. **Pre-deployment migration execution** to ensure zero-downtime
4. **Environment-specific migration handling**
5. **Rollback capabilities** for production safety

## 🔧 Migration Tools Setup

### 1. TypeORM Configuration

Create a dedicated TypeORM configuration file for migrations:

**File**: `src/config/typeorm.config.ts`

```typescript
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as entities from '../entities';

const configService = new ConfigService();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: configService.get('POSTGRES_HOST'),
  port: configService.get('POSTGRES_PORT'),
  username: configService.get('POSTGRES_USER'),
  password: configService.get('POSTGRES_PASSWORD'),
  database: configService.get('POSTGRES_DB'),
  entities: Object.values(entities),
  migrations: ['src/migrations/**/*.ts'],
  migrationsTableName: 'typeorm_migrations',
  synchronize: false, // Never use in production
  logging: ['error', 'migration'],
});
```

### 2. Package.json Scripts

Add migration scripts to `package.json`:

```json
{
  "scripts": {
    "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/config/typeorm.config.ts",
    "migration:create": "typeorm-ts-node-commonjs migration:create -d src/config/typeorm.config.ts",
    "migration:run": "typeorm-ts-node-commonjs migration:run -d src/config/typeorm.config.ts",
    "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/config/typeorm.config.ts",
    "migration:show": "typeorm-ts-node-commonjs migration:show -d src/config/typeorm.config.ts",
    "schema:sync": "typeorm-ts-node-commonjs schema:sync -d src/config/typeorm.config.ts",
    "schema:drop": "typeorm-ts-node-commonjs schema:drop -d src/config/typeorm.config.ts"
  }
}
```

### 3. Dependencies

Add required dependencies:

```bash
npm install --save-dev ts-node typeorm-ts-node-commonjs
```

## 📝 Migration Scripts

### 1. Database Migration Runner Script

**File**: `scripts/run-migrations.sh`

```bash
#!/bin/bash

# Database migration runner for AWS ECS
# Usage: ./run-migrations.sh <environment>

source "$(dirname "$0")/utils.sh"

ENVIRONMENT=${1:-dev}

print_status "Starting database migrations for environment: $ENVIRONMENT"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

# Check dependencies
check_aws

# Set environment variables from SSM
export POSTGRES_HOST=$(aws ssm get-parameter --name "/ivent/$ENVIRONMENT/db/host" --query 'Parameter.Value' --output text --region eu-central-1)
export POSTGRES_PORT=5432
export POSTGRES_USER=ivent_admin
export POSTGRES_DB="ivent_api_$ENVIRONMENT"
export POSTGRES_PASSWORD=$(aws ssm get-parameter --name "/ivent/$ENVIRONMENT/db/password" --with-decryption --query 'Parameter.Value' --output text --region eu-central-1)

print_status "Database connection configured for: $POSTGRES_HOST"

# Test database connectivity
print_status "Testing database connectivity..."
if ! pg_isready -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB"; then
    print_error "Cannot connect to database. Check connection parameters."
    exit 1
fi

# Show current migration status
print_status "Current migration status:"
npm run migration:show

# Run migrations
print_status "Running database migrations..."
if npm run migration:run; then
    print_status "✅ Database migrations completed successfully!"
else
    print_error "❌ Database migrations failed!"
    exit 1
fi

# Show final migration status
print_status "Final migration status:"
npm run migration:show

print_status "Migration process completed for environment: $ENVIRONMENT"
```

### 2. Migration Generation Helper

**File**: `scripts/generate-migration.sh`

```bash
#!/bin/bash

# Generate new migration based on entity changes
# Usage: ./generate-migration.sh <migration-name>

source "$(dirname "$0")/utils.sh"

if [ -z "$1" ]; then
    print_error "Migration name is required. Usage: ./generate-migration.sh <migration-name>"
    exit 1
fi

MIGRATION_NAME=$1

print_status "Generating migration: $MIGRATION_NAME"

# Ensure local database is running
if ! docker-compose ps postgres | grep -q "Up"; then
    print_status "Starting local PostgreSQL..."
    docker-compose up -d postgres
    sleep 10
fi

# Generate migration
npm run migration:generate -- "src/migrations/$MIGRATION_NAME"

print_status "Migration generated successfully!"
print_status "Review the generated migration file before committing."
```

## 🚀 ECS Integration

### 1. Migration Task Definition

**File**: `terraform/modules/ecs/migration-task.tf`

```hcl
# ECS Task Definition for Database Migrations
resource "aws_ecs_task_definition" "migration" {
  family                   = "${var.name_prefix}-migration"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "256"
  memory                   = "512"
  execution_role_arn       = aws_iam_role.ecs_task_execution.arn
  task_role_arn           = aws_iam_role.ecs_task.arn

  container_definitions = jsonencode([
    {
      name  = "migration"
      image = "${var.app_image}"

      essential = true

      command = ["/bin/sh", "-c", "npm run migration:run"]

      environment = [
        {
          name  = "NODE_ENV"
          value = var.environment
        },
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "POSTGRES_HOST"
          value = var.db_host
        },
        {
          name  = "POSTGRES_PORT"
          value = "5432"
        },
        {
          name  = "POSTGRES_USER"
          value = var.db_username
        },
        {
          name  = "POSTGRES_DB"
          value = var.db_name
        }
      ]

      secrets = [
        {
          name      = "POSTGRES_PASSWORD"
          valueFrom = "/ivent/${var.environment}/db/password"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.migration.name
          "awslogs-region"        = data.aws_region.current.name
          "awslogs-stream-prefix" = "migration"
        }
      }
    }
  ])

  tags = var.tags
}

# CloudWatch Log Group for Migrations
resource "aws_cloudwatch_log_group" "migration" {
  name              = "/ecs/${var.name_prefix}-migration"
  retention_in_days = 7

  tags = var.tags
}
```

### 2. Migration Execution Script

**File**: `scripts/run-ecs-migration.sh`

```bash
#!/bin/bash

# Run database migrations via ECS task
# Usage: ./run-ecs-migration.sh <environment>

source "$(dirname "$0")/utils.sh"

ENVIRONMENT=${1:-dev}
CLUSTER_NAME="ivent-api-$ENVIRONMENT-cluster"
TASK_DEFINITION="ivent-api-$ENVIRONMENT-migration"

print_status "Running database migration via ECS for environment: $ENVIRONMENT"

# Check AWS CLI and credentials
check_aws

# Get VPC configuration
VPC_CONFIG=$(aws ecs describe-services \
  --cluster "$CLUSTER_NAME" \
  --services "ivent-api-$ENVIRONMENT-service" \
  --query 'services[0].networkConfiguration.awsvpcConfiguration' \
  --output json \
  --region eu-central-1)

if [ "$VPC_CONFIG" = "null" ]; then
    print_error "Could not retrieve VPC configuration from existing service"
    exit 1
fi

# Run migration task
print_status "Starting migration task..."
TASK_ARN=$(aws ecs run-task \
  --cluster "$CLUSTER_NAME" \
  --task-definition "$TASK_DEFINITION" \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration=$VPC_CONFIG" \
  --query 'tasks[0].taskArn' \
  --output text \
  --region eu-central-1)

if [ "$TASK_ARN" = "None" ]; then
    print_error "Failed to start migration task"
    exit 1
fi

print_status "Migration task started: $TASK_ARN"

# Wait for task completion
print_status "Waiting for migration task to complete..."
aws ecs wait tasks-stopped \
  --cluster "$CLUSTER_NAME" \
  --tasks "$TASK_ARN" \
  --region eu-central-1

# Check task exit code
EXIT_CODE=$(aws ecs describe-tasks \
  --cluster "$CLUSTER_NAME" \
  --tasks "$TASK_ARN" \
  --query 'tasks[0].containers[0].exitCode' \
  --output text \
  --region eu-central-1)

if [ "$EXIT_CODE" = "0" ]; then
    print_status "✅ Database migration completed successfully!"
else
    print_error "❌ Database migration failed with exit code: $EXIT_CODE"

    # Show logs for debugging
    print_status "Migration task logs:"
    aws logs tail "/ecs/ivent-api-$ENVIRONMENT-migration" \
      --since 10m \
      --region eu-central-1

    exit 1
fi

print_status "Migration process completed successfully!"
```

## 🔄 Deployment Workflow

### 1. Updated Deployment Script

Update `terraform/scripts/deploy.sh` to include migration step:

```bash
# Add after successful terraform apply
if [ "$ACTION" = "apply" ]; then
    print_status "Deployment completed successfully!"

    # Run database migrations
    print_status "Running database migrations..."
    if ../../scripts/run-ecs-migration.sh "$ENVIRONMENT"; then
        print_status "✅ Database migrations completed!"
    else
        print_error "❌ Database migrations failed!"
        print_warning "Application may not function correctly without migrations."
    fi

    # Show outputs
    print_status "Deployment outputs:"
    terraform output
fi
```

### 2. CI/CD Integration

**File**: `.github/workflows/deploy-with-migrations.yml`

```yaml
name: Deploy with Database Migrations

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Build and push Docker image
        run: |
          # Build and push your Docker image
          # This should include your migration files

      - name: Deploy infrastructure
        run: |
          cd terraform
          ./scripts/deploy.sh ${{ inputs.environment }} apply

      - name: Run database migrations
        run: |
          ./scripts/run-ecs-migration.sh ${{ inputs.environment }}

      - name: Test deployment
        run: |
          ./scripts/test-deployment.sh ${{ inputs.environment }}
```

## 🎯 Best Practices

### 1. Migration Development Workflow

```bash
# 1. Make entity changes
# 2. Generate migration locally
./scripts/generate-migration.sh AddUserProfileFields

# 3. Review generated migration
# 4. Test migration locally
docker-compose up -d postgres
npm run migration:run

# 5. Test rollback
npm run migration:revert

# 6. Commit migration files
git add src/migrations/
git commit -m "feat: add user profile fields migration"
```

### 2. Production Safety Rules

- **Never run migrations directly in production**
- **Always test migrations in staging first**
- **Create database backups before major migrations**
- **Use separate migration tasks, not application containers**
- **Monitor migration execution time and impact**

### 3. Migration Naming Convention

```
YYYYMMDDHHMMSS-DescriptiveName.ts
Example: 20241221120000-AddUserProfileFields.ts
```

## 🔄 Rollback Procedures

### 1. Application Rollback

```bash
# Rollback to previous application version
aws ecs update-service \
  --cluster ivent-api-prod-cluster \
  --service ivent-api-prod-service \
  --task-definition ivent-api-prod-app:PREVIOUS_REVISION

# Run migration rollback if needed
./scripts/run-ecs-migration-rollback.sh prod
```

### 2. Database Rollback Script

**File**: `scripts/run-ecs-migration-rollback.sh`

```bash
#!/bin/bash

source "$(dirname "$0")/utils.sh"

ENVIRONMENT=${1:-dev}
CLUSTER_NAME="ivent-api-$ENVIRONMENT-cluster"
TASK_DEFINITION="ivent-api-$ENVIRONMENT-migration"

print_warning "⚠️  DANGER: Rolling back database migration!"
print_warning "This will revert the last applied migration."
read -p "Are you sure? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    print_status "Migration rollback cancelled."
    exit 0
fi

# Run rollback task with revert command
aws ecs run-task \
  --cluster "$CLUSTER_NAME" \
  --task-definition "$TASK_DEFINITION" \
  --launch-type FARGATE \
  --overrides '{
    "containerOverrides": [{
      "name": "migration",
      "command": ["/bin/sh", "-c", "npm run migration:revert"]
    }]
  }' \
  --region eu-central-1

print_status "Migration rollback initiated. Monitor logs for completion."
```

## 🔧 Troubleshooting

### Common Issues

1. **Migration Timeout**

   - Increase ECS task timeout
   - Split large migrations into smaller chunks
   - Use database connection pooling

2. **Permission Errors**

   - Verify IAM roles have SSM parameter access
   - Check database user permissions
   - Ensure VPC security groups allow database access

3. **Migration Conflicts**
   - Use `npm run migration:show` to check status
   - Manually resolve conflicts in database
   - Create corrective migrations

### Debugging Commands

```bash
# Check migration status
aws ecs run-task --cluster ivent-api-dev-cluster \
  --task-definition ivent-api-dev-migration \
  --overrides '{"containerOverrides":[{"name":"migration","command":["/bin/sh","-c","npm run migration:show"]}]}'

# View migration logs
aws logs tail /ecs/ivent-api-dev-migration --follow

# Connect to database directly (via bastion host)
psql -h ivent-api-dev-db.xxx.eu-central-1.rds.amazonaws.com -U ivent_admin -d ivent_api_dev
```

## 📊 Monitoring and Alerts

### CloudWatch Alarms

Add migration monitoring to your Terraform:

```hcl
resource "aws_cloudwatch_metric_alarm" "migration_failures" {
  alarm_name          = "${var.name_prefix}-migration-failures"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TasksStoppedReason"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors migration task failures"
  alarm_actions       = [var.sns_topic_arn]

  dimensions = {
    ClusterName = var.cluster_name
    Reason      = "Essential container in task exited"
  }
}
```

## 🚀 Next Steps

1. **Implement TypeORM configuration file**
2. **Add migration scripts to package.json**
3. **Create ECS migration task definition**
4. **Update deployment scripts**
5. **Test migration workflow in development**
6. **Set up monitoring and alerts**

This comprehensive migration strategy ensures safe, reliable database schema management across all environments while maintaining zero-downtime deployments.
