# 🔧 Script Management & Secret Handling Guide

This guide explains the relationship between deployment scripts, secret management, and Terraform configuration in the ivent-api project.

## 📋 Table of Contents

- [Secret Management Analysis](#-secret-management-analysis)
- [Script Documentation](#-script-documentation)
- [Best Practices](#-best-practices)
- [Recommended Workflows](#-recommended-workflows)
- [Troubleshooting](#-troubleshooting)

## 🔐 Secret Management Analysis

### Current Setup Overview

The project has **two approaches** for managing AWS Systems Manager Parameter Store secrets:

#### Approach A: Shell Script Method
- **File**: `scripts/setup-secrets.sh`
- **Method**: Interactive prompts + AWS CLI
- **Timing**: Before Terraform deployment
- **State**: Outside Terraform state management

#### Approach B: Terraform Resources
- **File**: `terraform/main.tf` (lines 167-181)
- **Method**: Terraform resources with variables
- **Timing**: During Terraform deployment
- **State**: Managed in Terraform state

### ⚠️ Potential Conflicts

1. **State Management Drift**: Parameters created via script won't be in Terraform state
2. **Value Overwriting**: Both use `overwrite = true`, last one wins
3. **Plan Inconsistencies**: Terraform may try to recreate existing parameters

## 📚 Script Documentation

### 🔑 `scripts/setup-secrets.sh`

**Purpose**: Interactive secret setup for AWS Systems Manager Parameter Store

**Usage**:
```bash
./scripts/setup-secrets.sh <environment>
```

**What it does**:
- ✅ Prompts for database password (hidden input)
- ✅ Prompts for JWT secret (hidden input)
- ✅ Creates/updates SSM parameters:
  - `/ivent/{env}/db/password`
  - `/ivent/{env}/jwt/secret`
- ✅ Creates Terraform state S3 bucket if needed
- ✅ Configures bucket versioning and encryption

**Prerequisites**:
- AWS CLI installed and configured
- IAM permissions for SSM and S3 operations

**Example**:
```bash
./scripts/setup-secrets.sh dev
# Enter value for /ivent/dev/db/password: [hidden input]
# Enter value for /ivent/dev/jwt/secret: [hidden input]
```

### 🐳 `scripts/setup-local-s3.sh`

**Purpose**: Sets up local S3 buckets using LocalStack for development

**Usage**:
```bash
./scripts/setup-local-s3.sh
```

**What it does**:
- ✅ Checks LocalStack health at `localhost:4566`
- ✅ Creates local S3 bucket with folder structure:
  - `vibes/`
  - `thumbnails/`
  - `thumbnails/vibes/`
  - `ivents/`
  - `memories/`
- ✅ Configures CORS settings for local development
- ✅ Uses LocalStack endpoint instead of real AWS

**Prerequisites**:
- LocalStack running: `docker-compose up localstack`
- AWS CLI installed

**When to use**:
- 🔧 Local development without AWS costs
- 🧪 CI/CD testing pipelines
- 🚀 Development environment setup

### 🧪 `scripts/test-deployment.sh`

**Purpose**: Comprehensive deployment testing and health verification

**Usage**:
```bash
./scripts/test-deployment.sh <environment> [load-balancer-dns]
```

**What it does**:
- ✅ **15 comprehensive tests** including:
  - Health check endpoints
  - API documentation availability
  - Response time validation (< 2 seconds)
  - Load balancer target health
  - ECS service status and task counts
  - Database connectivity (indirect)
  - Security headers validation
  - CORS functionality
  - CloudWatch logs availability
  - S3 bucket accessibility
  - Parameter Store secrets access
- ✅ **Optional load testing** with Artillery
- ✅ **Detailed reporting** with pass/fail counts
- ✅ **Troubleshooting guidance** for failed tests

**Prerequisites**:
- Deployed infrastructure (ECS, ALB, RDS, etc.)
- AWS CLI configured
- `bc` command for mathematical operations
- Optional: Artillery (`npm install -g artillery`)

**Examples**:
```bash
# Auto-detect ALB DNS from Terraform output
./scripts/test-deployment.sh dev

# Specify ALB DNS manually
./scripts/test-deployment.sh dev your-alb-dns.elb.amazonaws.com
```

## 🎯 Best Practices

### Recommended Approach: Terraform-First

**Why Terraform-First?**
- ✅ Infrastructure as Code consistency
- ✅ Better state management
- ✅ Easier to reproduce across environments
- ✅ Better CI/CD integration
- ✅ Complete audit trail

### Security Best Practices

1. **Use Environment Variables**:
   ```bash
   export TF_VAR_db_password="$(openssl rand -base64 32)"
   export TF_VAR_jwt_secret="$(openssl rand -base64 64)"
   ```

2. **Never Commit Secrets**:
   - Use `.env` files (in `.gitignore`)
   - Use CI/CD secret management
   - Use AWS Secrets Manager for production

3. **Regular Secret Rotation**:
   ```bash
   # Via script
   ./scripts/setup-secrets.sh prod
   
   # Via Terraform
   terraform apply -var="db_password=new-password"
   ```

## 🚀 Recommended Workflows

### Option 1: Terraform-First (Recommended)

```bash
# 1. Set secrets as environment variables
export TF_VAR_db_password="your-secure-password"
export TF_VAR_jwt_secret="your-jwt-secret"

# 2. Deploy infrastructure
cd terraform
terraform init
terraform plan -var-file="environments/dev/terraform.tfvars"
terraform apply -var-file="environments/dev/terraform.tfvars"

# 3. Test deployment
cd ..
./scripts/test-deployment.sh dev
```

### Option 2: Script-First

```bash
# 1. Create secrets interactively
./scripts/setup-secrets.sh dev

# 2. Deploy with Terraform (imports existing parameters)
cd terraform
terraform apply -var-file="environments/dev/terraform.tfvars" \
  -var="db_password=dummy" \
  -var="jwt_secret=dummy"
# Note: Terraform uses existing SSM values, not dummy values

# 3. Test deployment
cd ..
./scripts/test-deployment.sh dev
```

### Complete Deployment Sequence

```bash
# 1. Setup secrets (choose approach)
./scripts/setup-secrets.sh dev
# OR set TF_VAR_* environment variables

# 2. Deploy infrastructure
cd terraform
terraform init
terraform plan -var-file="environments/dev/terraform.tfvars"
terraform apply -var-file="environments/dev/terraform.tfvars"

# 3. Build and push Docker image
# (Build your application and push to ECR)

# 4. Test deployment
./scripts/test-deployment.sh dev

# 5. Optional: Local development setup
./scripts/setup-local-s3.sh  # For local development only
```

## 🔧 Troubleshooting

### Common Issues

1. **Script vs Terraform Conflicts**:
   ```bash
   # Check current SSM parameters
   aws ssm get-parameters --names "/ivent/dev/db/password" "/ivent/dev/jwt/secret"
   
   # Check Terraform state
   terraform state list | grep ssm_parameter
   ```

2. **Missing print_test Function**:
   - Fixed in `scripts/utils.sh` with BLUE color support

3. **LocalStack Not Running**:
   ```bash
   # Check LocalStack health
   curl -s http://localhost:4566/_localstack/health
   
   # Start LocalStack
   docker-compose up localstack
   ```

4. **Test Failures**:
   ```bash
   # Check ECS service logs
   aws logs tail /ecs/ivent-api-dev --follow
   
   # Check service status
   aws ecs describe-services --cluster ivent-api-dev-cluster --services ivent-api-dev-service
   ```

### Script Execution Issues

1. **Permission Denied**:
   ```bash
   chmod +x scripts/*.sh
   ```

2. **AWS CLI Not Configured**:
   ```bash
   aws configure
   # OR
   aws sts get-caller-identity  # Test current config
   ```

3. **Missing Dependencies**:
   ```bash
   # Install bc for mathematical operations
   brew install bc  # macOS
   sudo apt-get install bc  # Ubuntu
   
   # Install Artillery for load testing
   npm install -g artillery
   ```

---

## 📝 Summary

- **Use Terraform-first approach** for consistency and state management
- **Keep scripts for operational tasks**: secret rotation, local development, testing
- **Follow security best practices**: environment variables, no committed secrets
- **Test deployments thoroughly** with the comprehensive test script
- **Use LocalStack for local development** to avoid AWS costs

This approach provides infrastructure consistency while maintaining operational flexibility! 🎯
