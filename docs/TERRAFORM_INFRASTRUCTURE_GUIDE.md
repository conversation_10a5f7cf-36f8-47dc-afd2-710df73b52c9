# Terraform Infrastructure Guide

This comprehensive guide explains the complete Terraform infrastructure setup for the ivent-api NestJS application deployment on AWS.

## 📁 Directory Structure Overview

```
terraform/
├── main.tf                    # Root configuration orchestrating all modules
├── variables.tf               # Root-level variable definitions
├── outputs.tf                 # Root-level outputs
├── environments/              # Environment-specific configurations
│   ├── dev/
│   │   └── terraform.tfvars   # Development environment variables
│   ├── staging/
│   │   └── terraform.tfvars   # Staging environment variables
│   └── prod/
│       └── terraform.tfvars   # Production environment variables
├── modules/                   # Reusable Terraform modules
│   ├── vpc/                   # Virtual Private Cloud setup
│   ├── security-groups/       # Security group configurations
│   ├── rds/                   # PostgreSQL database setup
│   ├── s3/                    # S3 storage buckets
│   ├── ecs/                   # Container orchestration
│   └── monitoring/            # CloudWatch monitoring & alerts
└── scripts/
    └── deploy.sh              # Deployment automation script
```

## 🏗️ Infrastructure Architecture

### High-Level Architecture Flow

```
Internet Gateway
       ↓
Application Load Balancer (Public Subnets)
       ↓
ECS Fargate Tasks (Private Subnets)
       ↓
RDS PostgreSQL (Private Subnets)
       ↓
S3 Buckets (Media Storage)
```

### Network Architecture

- **VPC**: Isolated network environment (10.0.0.0/16)
- **Public Subnets**: ALB and NAT Gateways (********/24, ********/24)
- **Private Subnets**: ECS tasks and RDS (**********/24, **********/24)
- **Multi-AZ**: Resources distributed across 2 availability zones

## 📋 Module Detailed Documentation

### 1. VPC Module (`modules/vpc/`)

**Purpose**: Creates the foundational network infrastructure

**Key Resources:**

- VPC with DNS support enabled
- Internet Gateway for public internet access
- Public/Private subnets across multiple AZs
- NAT Gateways for private subnet internet access
- Route tables and associations

**Variables:**

- `name_prefix`: Resource naming prefix
- `vpc_cidr`: VPC CIDR block (default: 10.0.0.0/16)
- `availability_zones`: List of AZs to use
- `public_subnet_cidrs`: CIDR blocks for public subnets
- `private_subnet_cidrs`: CIDR blocks for private subnets

**Outputs:**

- `vpc_id`: VPC identifier
- `public_subnet_ids`: List of public subnet IDs
- `private_subnet_ids`: List of private subnet IDs
- `internet_gateway_id`: Internet gateway ID

### 2. Security Groups Module (`modules/security-groups/`)

**Purpose**: Defines network access rules for different components

**Security Groups Created:**

- **ALB Security Group**: Allows HTTP/HTTPS from internet
- **App Security Group**: Allows traffic from ALB on port 3000
- **Database Security Group**: Allows PostgreSQL access from app

**Key Rules:**

```
ALB SG:     Internet → Port 80/443
App SG:     ALB SG → Port 3000
DB SG:      App SG → Port 5432
```

**Variables:**

- `name_prefix`: Resource naming prefix
- `vpc_id`: VPC where security groups are created
- `tags`: Resource tags

### 3. RDS Module (`modules/rds/`)

**Purpose**: Manages PostgreSQL database infrastructure

**Key Features:**

- PostgreSQL 15.8 engine
- Encrypted storage with KMS
- Automated backups with 1-day retention
- Performance Insights enabled
- Enhanced monitoring with CloudWatch
- Multi-AZ deployment option

**Variables:**

- `name_prefix`: Resource naming prefix
- `vpc_id`: VPC for database subnet group
- `private_subnet_ids`: Subnets for database placement
- `security_group_ids`: Security groups for database access
- `instance_class`: Database instance type (default: db.t3.micro)
- `allocated_storage`: Initial storage size (default: 20GB)
- `db_name`: Database name
- `username`: Master username
- `password`: Master password (sensitive)

**Outputs:**

- `db_instance_id`: Database instance identifier
- `db_endpoint`: Database connection endpoint
- `db_port`: Database port (5432)

### 4. S3 Module (`modules/s3/`)

**Purpose**: Provides object storage for media files and backups

**Buckets Created:**

- **Media Bucket**: Stores application media files
- **Backup Bucket**: Stores database backups and logs

**Key Features:**

- Server-side encryption (AES256)
- Versioning enabled
- Lifecycle policies for cost optimization
- CORS configuration for web access
- Public access blocked by default

**Lifecycle Policies:**

- Transition to IA after 30 days
- Transition to Glacier after 90 days
- Delete old versions after specified period

**Variables:**

- `name_prefix`: Resource naming prefix
- `tags`: Resource tags

### 5. ECS Module (`modules/ecs/`)

**Purpose**: Container orchestration and application deployment

**Key Components:**

- **ECS Cluster**: Fargate-based container cluster
- **Task Definition**: Container specifications
- **ECS Service**: Manages desired task count
- **Application Load Balancer**: Routes traffic to containers
- **ECR Repository**: Stores Docker images
- **IAM Roles**: Task execution and task roles

**Container Configuration:**

- CPU: 256 units (0.25 vCPU)
- Memory: 512 MB
- Port: 3000
- Health check: /health endpoint

**Variables:**

- `name_prefix`: Resource naming prefix
- `vpc_id`: VPC for load balancer
- `public_subnet_ids`: Subnets for load balancer
- `private_subnet_ids`: Subnets for ECS tasks
- `security_group_ids`: Security groups for resources
- `db_endpoint`: Database connection string
- `s3_bucket_name`: S3 bucket for media storage

### 6. Monitoring Module (`modules/monitoring/`)

**Purpose**: Observability and alerting infrastructure

**Key Components:**

- **CloudWatch Dashboard**: Visual metrics overview
- **CloudWatch Alarms**: Automated alerting
- **SNS Topic**: Alert notifications

**Monitored Metrics:**

- ECS CPU/Memory utilization
- ALB response times and error rates
- RDS CPU, connections, and storage
- Custom application metrics

**Alert Thresholds:**

- CPU > 80% for 2 consecutive periods
- Memory > 80% for 2 consecutive periods
- Response time > 2 seconds
- 5XX errors > 10 per period
- RDS free storage < 2GB

## 🔧 Variable Configuration Guide

### Root Variables (`variables.tf`)

**Required Variables:**

- `environment`: Environment name (dev/staging/prod)
- `aws_region`: AWS region for deployment
- `db_password`: Database master password (sensitive)
- `jwt_secret`: JWT signing secret (sensitive)

**Optional Variables:**

- `alert_email`: Email for CloudWatch alerts
- `app_port`: Application port (default: 3000)
- `desired_count`: Number of ECS tasks (default: 1)

### Environment-Specific Variables

**Development (`environments/dev/terraform.tfvars`):**

```hcl
environment = "dev"
aws_region  = "eu-central-1"

# Networking
vpc_cidr = "10.0.0.0/16"
availability_zones = ["eu-central-1a", "eu-central-1b"]

# Database
db_instance_class = "db.t3.micro"
db_allocated_storage = 20

# ECS
ecs_cpu = "256"
ecs_memory = "512"
desired_count = 1
```

**Production (`environments/prod/terraform.tfvars`):**

```hcl
environment = "prod"
aws_region  = "eu-central-1"

# Database (larger instance for production)
db_instance_class = "db.t3.small"
db_allocated_storage = 100
multi_az = true

# ECS (more resources for production)
ecs_cpu = "512"
ecs_memory = "1024"
desired_count = 2
```

## 🔄 Module Interconnections

### Data Flow Between Modules:

1. **VPC** → Provides network foundation for all other modules
2. **Security Groups** → Uses VPC ID, provides security group IDs
3. **RDS** → Uses VPC ID, private subnets, and database security group
4. **S3** → Independent but referenced by ECS for storage
5. **ECS** → Uses VPC, subnets, security groups, RDS endpoint, S3 bucket
6. **Monitoring** → References all other modules for metric collection

### Dependency Chain:

```
VPC → Security Groups → RDS
 ↓         ↓           ↓
 └→ ECS ←──┴───────────┘
    ↓
Monitoring
```

## 🚀 Deployment Commands

### Initialize and Deploy:

```bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var-file="environments/dev/terraform.tfvars" \
  -var="db_password=your_password" \
  -var="jwt_secret=your_jwt_secret"

# Apply changes
terraform apply -var-file="environments/dev/terraform.tfvars" \
  -var="db_password=your_password" \
  -var="jwt_secret=your_jwt_secret"
```

### Using the Deployment Script:

```bash
# Deploy to development
./scripts/deploy.sh dev apply

# Deploy to production
./scripts/deploy.sh prod apply
```

## 🔧 Configuration Modification Guide

### Adding New Environment:

1. Create `environments/new-env/terraform.tfvars`
2. Define environment-specific variables
3. Update backend configuration if needed
4. Deploy using standard commands

### Scaling Resources:

- **ECS**: Modify `desired_count` in tfvars
- **Database**: Change `db_instance_class` and `db_allocated_storage`
- **Network**: Add more subnets and AZs if needed

### Adding New Services:

1. Create new module in `modules/`
2. Define variables, resources, and outputs
3. Reference in root `main.tf`
4. Update environment tfvars as needed

## 📊 Outputs Reference

### Key Infrastructure Outputs:

- `app_url`: Application load balancer URL
- `database_endpoint`: RDS connection endpoint
- `ecr_repository_url`: Docker image repository
- `cloudwatch_dashboard_url`: Monitoring dashboard
- `s3_media_bucket_name`: Media storage bucket

### Usage in Applications:

These outputs can be used to configure your application or CI/CD pipelines:

```bash
# Get database endpoint
terraform output database_endpoint

# Get ECR repository for Docker push
terraform output ecr_repository_url
```

This infrastructure provides a production-ready, scalable, and secure foundation for your NestJS application with proper separation of concerns and environment-specific configurations.
