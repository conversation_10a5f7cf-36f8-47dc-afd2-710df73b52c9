import { ApiProperty } from '@nestjs/swagger';
import { IventCreatorTypeEnum, IventViewTypeEnum } from 'src/entities';

export class IventListItem {
  @ApiProperty({
    description: 'UUID of the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeFolderId!: string;

  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the ivent thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl?: string | null;

  @ApiProperty({
    description: 'Name of the ivent location',
    example: 'Central Park',
  })
  locationName!: string;

  @ApiProperty({
    description: 'List of dates for the ivent in ISO 8601 date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[];

  @ApiProperty({
    description: 'UUID of the ivent creator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creatorId!: string;

  @ApiProperty({
    enum: IventCreatorTypeEnum,
    enumName: 'IventCreatorTypeEnum',
    description: 'Type of the ivent creator',
    example: IventCreatorTypeEnum.USER,
  })
  creatorType!: IventCreatorTypeEnum;

  @ApiProperty({
    description: 'Username of the ivent creator',
    example: 'john_doe',
  })
  creatorUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the ivent creator image',
    example: 'https://example.com/creator-image.jpg',
    format: 'url',
  })
  creatorImageUrl?: string | null;

  @ApiProperty({
    type: 'integer',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];

  @ApiProperty({
    type: 'array',
    items: { type: 'string', nullable: true },
    format: 'url',
    description: 'List of member avatar URLs in the ivent',
    example: ['https://example.com/avatar1.jpg', 'https://example.com/avatar2.jpg'],
  })
  memberAvatarUrls!: (string | null)[];

  @ApiProperty({
    enum: IventViewTypeEnum,
    enumName: 'IventViewTypeEnum',
    description: 'View type of the ivent for the current user',
    example: IventViewTypeEnum.DEFAULT,
  })
  viewType!: IventViewTypeEnum;
}
