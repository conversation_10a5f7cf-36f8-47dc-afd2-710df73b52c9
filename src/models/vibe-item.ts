import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, MediaFormatEnum } from 'src/entities';

export class VibeItem {
  @ApiProperty({
    description: 'UUID of the vibe',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeId!: string;

  @ApiProperty({
    description: 'UUID of the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeFolderId!: string;

  @ApiProperty({
    description: 'URL to the vibe media',
    example: 'https://example.com/vibe.jpg',
    format: 'url',
  })
  mediaUrl!: string;

  @ApiProperty({
    enum: MediaFormatEnum,
    enumName: 'MediaFormatEnum',
    description: 'Format of the media',
    example: MediaFormatEnum.IMAGE,
  })
  mediaFormat!: MediaFormatEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the vibe thumbnail',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl?: string | null;

  @ApiProperty({
    description: 'Caption of the vibe',
    example: 'This is a vibe',
  })
  caption!: string;

  @ApiProperty({
    description: 'UUID of the creator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creatorId!: string;

  @ApiProperty({
    enum: AccountTypeEnum,
    enumName: 'AccountTypeEnum',
    description: 'Type of the creator',
    example: AccountTypeEnum.USER,
  })
  creatorType!: AccountTypeEnum;

  @ApiProperty({
    description: 'Username of the creator',
    example: 'john_doe',
  })
  creatorUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the creator image',
    example: 'https://example.com/creator-image.jpg',
    format: 'url',
  })
  creatorAvatarUrl?: string | null;

  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    description: 'List of dates for the ivent, in ISO 8601 date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Number of likes on the vibe',
    example: 10,
    minimum: 0,
  })
  likeCount!: number;

  @ApiProperty({
    type: 'integer',
    description: 'Number of comments on the vibe',
    example: 5,
    minimum: 0,
  })
  commentCount!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'UUID of the next vibe in the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  nextVibeId?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'UUID of the previous vibe in the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  previousVibeId?: string | null;

  @ApiProperty({
    type: 'integer',
    description: 'Index of the vibe in the vibe folder',
    example: 0,
    minimum: 0,
  })
  vibeIndex!: number;

  @ApiProperty({
    type: 'integer',
    description: 'Total number of vibes in the vibe folder',
    example: 10,
    minimum: 0,
  })
  vibeCount!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Date of creation of the vibe, in ISO 8601 date-time format',
    example: '2024-08-31T22:00:00Z',
  })
  createdAt?: string | null;
}
