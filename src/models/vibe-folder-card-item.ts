import { ApiProperty } from '@nestjs/swagger';

export class VibeFolderCardItem {
  @ApiProperty({
    description: 'UUID of the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeFolderId!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the vibe folder thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl?: string | null;

  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    description: 'UUID of the latest vibe in the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeId!: string;

  @ApiProperty({
    type: 'integer',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];
}
