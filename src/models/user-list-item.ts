import { ApiProperty } from '@nestjs/swagger';
import { GroupMembershipStatusEnum, PageMembershipStatusEnum, UserRelationshipStatusEnum } from 'src/entities';

export class UserListItem {
  @ApiProperty({
    description: 'UUID of the user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string;

  @ApiProperty({
    description: 'Username of the user',
    example: 'john_doe',
  })
  username!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: "Name of the user's university",
    example: 'Boğaziçi University',
  })
  university?: string | null;
}

export class UserListItemWithPhoneNumber extends UserListItem {
  @ApiProperty({
    description: 'Phone number of the user',
    example: '+90(500)4003020',
  })
  phoneNumber!: string;
}

export class UserListItemWithRelationshipStatus extends UserListItem {
  @ApiProperty({
    enum: UserRelationshipStatusEnum,
    enumName: 'UserRelationshipStatusEnum',
    description: 'Status of the relationship between the current user and the user in the list',
    example: UserRelationshipStatusEnum.ACCEPTED,
  })
  relationshipStatus!: UserRelationshipStatusEnum;
}

export class UserListItemWithGroupRole extends UserListItem {
  @ApiProperty({
    enum: UserRelationshipStatusEnum,
    enumName: 'UserRelationshipStatusEnum',
    description: 'Status of the relationship between the current user and the user in the list',
    example: UserRelationshipStatusEnum.ACCEPTED,
  })
  relationshipStatus!: UserRelationshipStatusEnum;

  @ApiProperty({
    enum: GroupMembershipStatusEnum,
    enumName: 'GroupMembershipStatusEnum',
    description: 'Status of the group membership of the user',
    example: GroupMembershipStatusEnum.ADMIN,
  })
  groupMembershipStatus!: GroupMembershipStatusEnum;
}

export class UserListItemWithPageRole extends UserListItem {
  @ApiProperty({
    enum: PageMembershipStatusEnum,
    enumName: 'PageMembershipStatusEnum',
    description: 'Status of the page membership of the user',
    example: PageMembershipStatusEnum.ADMIN,
  })
  pageMembershipStatus!: PageMembershipStatusEnum;
}
