import { ApiProperty } from '@nestjs/swagger';
import { IventCreatorTypeEnum } from 'src/entities';

export class IventCardItem {
  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the ivent thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl?: string | null;

  @ApiProperty({
    description: 'Name of the ivent location',
    example: 'Central Park',
  })
  locationName!: string;

  @ApiProperty({
    description: 'UUID of the ivent creator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creatorId!: string;

  @ApiProperty({
    enum: IventCreatorTypeEnum,
    enumName: 'IventCreatorTypeEnum',
    description: 'Type of the ivent creator',
    example: IventCreatorTypeEnum.USER,
  })
  creatorType!: IventCreatorTypeEnum;

  @ApiProperty({
    description: 'Username of the ivent creator',
    example: 'john_doe',
  })
  creatorUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the ivent creator image',
    example: 'https://example.com/creator-image.jpg',
    format: 'url',
  })
  creatorImageUrl?: string | null;

  @ApiProperty({
    description: 'Whether the ivent is favorited by the current user',
    example: true,
  })
  isFavorited!: boolean;
}
