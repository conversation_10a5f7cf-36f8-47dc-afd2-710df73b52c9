import { ApiProperty } from '@nestjs/swagger';

export class MemoryItem {
  @ApiProperty({
    description: 'UUID of the memory',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  memoryId!: string;

  @ApiProperty({
    description: 'URL to the memory media',
    example: 'https://example.com/memory.jpg',
    format: 'url',
  })
  mediaUrl!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the memory thumbnail',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl?: string | null;

  @ApiProperty({
    description: 'Caption of the memory',
    example: 'This is a memory',
  })
  caption!: string;

  @ApiProperty({
    description: 'UUID of the memory creator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creatorId!: string;

  @ApiProperty({
    description: 'Username of the memory creator',
    example: 'john_doe',
  })
  creatorUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the memory creator image',
    example: 'https://example.com/creator-image.jpg',
    format: 'url',
  })
  creatorAvatarUrl?: string | null;

  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    isArray: true,
    nullable: true,
    required: false,
    description: 'List of dates for the ivent in ISO 8601 date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[] | null;

  @ApiProperty({
    type: 'integer',
    nullable: true,
    required: false,
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount?: number | null;

  @ApiProperty({
    type: 'string',
    isArray: true,
    nullable: true,
    required: false,
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[] | null;
}
