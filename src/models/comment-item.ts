import { ApiProperty } from '@nestjs/swagger';

export class CommentItem {
  @ApiProperty({
    format: 'uuid',
    description: 'UUID of the comment',
  })
  commentId!: string;

  @ApiProperty({
    description: 'Text content of the comment',
  })
  comment!: string;

  @ApiProperty({
    format: 'uuid',
    description: 'UUID of the user who made the comment',
  })
  commenterUserId!: string;

  @ApiProperty({
    description: 'Username of the user who made the comment',
    example: 'john_doe',
  })
  commenterUsername!: string;

  @ApiProperty({
    description: 'Timestamp when the comment was created in ISO 8601 date-time format',
    example: '2023-12-01T10:30:00Z',
  })
  createdAt!: string;
}
