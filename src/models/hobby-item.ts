import { ApiProperty } from '@nestjs/swagger';

export class HobbyItem {
  @ApiProperty({
    description: 'UUID of the hobby',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  hobbyId!: string;

  @ApiProperty({
    description: 'Name of the hobby (sub category)',
    example: 'Photography',
  })
  hobbyName!: string;

  @ApiProperty({
    description: 'UUID of the parent hobby',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  parentHobbyId!: string;

  @ApiProperty({
    description: 'Name of the parent hobby (main category)',
    example: 'Art',
  })
  parentHobbyName!: string;
}
