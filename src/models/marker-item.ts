import { ApiProperty } from '@nestjs/swagger';

export class MarkerItem {
  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    type: 'double',
    description: 'Latitude coordinate of the ivent',
    example: 40.7766,
    minimum: -90,
    maximum: 90,
  })
  latitude!: number;

  @ApiProperty({
    type: 'double',
    description: 'Longitude coordinate of the ivent',
    example: -73.9712,
    minimum: -180,
    maximum: 180,
  })
  longitude!: number;
}
