import { DeleteObjectCommand, GetObjectCommand, HeadObjectCommand, PutO<PERSON>Command, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface UploadResult {
  url: string;
  key: string;
}

export interface PresignedUrlOptions {
  expiresIn?: number; // seconds, default 3600 (1 hour)
  contentType?: string;
}

@Injectable()
export class AwsS3Service implements OnModuleInit {
  private readonly logger = new Logger(AwsS3Service.name);
  private s3Client: S3Client;
  private bucketName: string;

  constructor(private configService: ConfigService) {}

  onModuleInit() {
    const region = this.configService.get<string>('AWS_REGION') || 'eu-central-1';
    this.bucketName = this.configService.get<string>('AWS_S3_BUCKET_NAME');

    if (!this.bucketName) {
      throw new Error('AWS_S3_BUCKET_NAME is not defined in environment variables');
    }

    // Configure S3 client
    const s3Config: any = {
      region,
    };

    // For local development with LocalStack
    const endpointUrl = this.configService.get<string>('AWS_ENDPOINT_URL');
    if (endpointUrl) {
      s3Config.endpoint = endpointUrl;
      s3Config.forcePathStyle = true;
      s3Config.credentials = {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID') || 'test',
        secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY') || 'test',
      };
    }

    this.s3Client = new S3Client(s3Config);
    this.logger.log(`S3 service initialized with bucket: ${this.bucketName}`);
  }

  /**
   * Upload a file from base64 data
   */
  async uploadBase64Image(base64Data: string, fileName: string, folderName: string, contentType: string = 'image/jpeg'): Promise<UploadResult> {
    try {
      // Remove data URL prefix if present
      const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');

      // Convert base64 to buffer
      const buffer = Buffer.from(base64String, 'base64');

      const key = `${folderName}/${fileName}`;

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        ServerSideEncryption: 'AES256',
      });

      await this.s3Client.send(command);

      // Generate signed URL for access
      const url = await this.getSignedUrl(key, { expiresIn: 86400 }); // 24 hours

      this.logger.log(`Successfully uploaded image: ${key}`);
      return { url, key };
    } catch (error) {
      this.logger.error(`Failed to upload base64 image: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Upload a file from buffer (for multipart uploads)
   */
  async uploadFile(file: Express.Multer.File, fileName: string, folderName: string): Promise<UploadResult> {
    try {
      const key = `${folderName}/${fileName}`;

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ServerSideEncryption: 'AES256',
      });

      await this.s3Client.send(command);

      // Generate signed URL for access
      const url = await this.getSignedUrl(key, { expiresIn: 86400 }); // 24 hours

      this.logger.log(`Successfully uploaded file: ${key}`);
      return { url, key };
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a signed URL for accessing a file
   */
  async getSignedUrl(key: string, options: PresignedUrlOptions = {}): Promise<string> {
    try {
      const { expiresIn = 3600, contentType } = options;

      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        ResponseContentType: contentType,
      });

      const url = await getSignedUrl(this.s3Client, command, {
        expiresIn,
      });

      return url;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL for ${key}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.log(`Successfully deleted file: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${key}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a file exists in S3
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        return false;
      }
      this.logger.error(`Failed to check file existence ${key}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get presigned URL for direct upload from client
   */
  async getPresignedUploadUrl(key: string, contentType: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        ContentType: contentType,
        ServerSideEncryption: 'AES256',
      });

      const url = await getSignedUrl(this.s3Client, command, {
        expiresIn,
      });

      return url;
    } catch (error) {
      this.logger.error(`Failed to generate presigned upload URL for ${key}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
