// src/firebase/firebase-storage.service.ts
import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';

@Injectable()
export class FirebaseStorageService implements OnModuleInit {
  private storage!: admin.storage.Storage;

  constructor(private configService: ConfigService) {}

  onModuleInit() {
    if (!admin.apps.length) {
      const serviceAccount = {
        projectId: this.configService.get('FIREBASE_PROJECT_ID'),
        privateKey: this.configService.get('FIREBASE_PRIVATE_KEY').replace(/\\n/g, '\n'),
        clientEmail: this.configService.get('FIREBASE_CLIENT_EMAIL'),
      };

      const storageBucket = this.configService.get<string>('FIREBASE_STORAGE_BUCKET');
      if (!storageBucket) {
        throw new Error('FIREBASE_STORAGE_BUCKET is not defined in environment variables');
      }
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        storageBucket: storageBucket,
      });
    }

    this.storage = admin.storage();
  }

  async uploadImage(base64Data: string, id: string, folderName: string): Promise<{ url: string }> {
    // Remove data URL prefix if present (e.g., "data:image/jpeg;base64,")
    const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');

    // Convert base64 string to Buffer
    const buffer = Buffer.from(base64String, 'base64');

    const bucket = this.storage.bucket();
    const fileRef = bucket.file(`${folderName}/${id}.jpg`);

    await fileRef.save(buffer, {
      metadata: { contentType: 'image/jpeg' },
    });

    const url = await this.getSignedUrl(id, folderName, 'jpg', '01-01-2500');
    return { url };
  }

  async uploadVibe(vibeId: string, file: Express.Multer.File): Promise<void> {
    console.log(file.mimetype);
    console.log(file.originalname);
    const extension = file.mimetype.includes('video') ? 'mp4' : 'jpg';

    const bucket = this.storage.bucket();
    const fileRef = bucket.file(`vibes/${vibeId}.${extension}`);

    await fileRef.save(file.buffer, {
      metadata: {
        contentType: file.mimetype,
      },
    });
  }

  async getSignedUrl(
    fileName: string,
    folderName: string,
    extension: string,
    expirationTime: string | number | Date,
  ): Promise<string> {
    const bucket = this.storage.bucket();
    const fileRef = bucket.file(`${folderName}/${fileName}.${extension}`);
    const [url] = await fileRef.getSignedUrl({
      action: 'read',
      expires: expirationTime,
    });
    return url;
  }
}
