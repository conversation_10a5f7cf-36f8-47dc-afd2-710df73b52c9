import { ApiProperty } from '@nestjs/swagger';
import { UserListItemWithRelationshipStatus } from 'src/models';
import { CommentItem } from 'src/models/comment-item';
import { VibeItem } from 'src/models/vibe-item';

export class CreateVibeReturn {
  @ApiProperty({
    description: 'UUID of the newly created vibe',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeId!: string;
}

export class GetCommentsByVibeIdReturn {
  @ApiProperty({
    type: [CommentItem],
    description: 'List of comments on the vibe',
    example: [],
  })
  comments!: CommentItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of comments on the vibe',
    example: 0,
    minimum: 0,
  })
  commentCount!: number;
}

export class GetLikesByVibeIdReturn {
  @ApiProperty({
    type: [UserListItemWithRelationshipStatus],
    description: 'List of users who liked the vibe',
    example: [],
  })
  likes!: UserListItemWithRelationshipStatus[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of likes on the vibe',
    example: 0,
    minimum: 0,
  })
  likeCount!: number;
}

export class GetVibeByVibeIdReturn extends VibeItem {}

export class GetVibesReturn {
  @ApiProperty({
    type: [VibeItem],
    description: 'List of vibes',
    example: [],
  })
  vibes!: VibeItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of vibes',
    example: 0,
    minimum: 0,
  })
  vibeCount!: number;
}
