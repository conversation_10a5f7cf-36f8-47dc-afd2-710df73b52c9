import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  FileTypeValidator,
  Get,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateVibeDto, UpdateByVibeIdDto } from './models/vibes.dto';
import {
  CreateVibeReturn,
  GetCommentsByVibeIdReturn,
  GetLikesByVibeIdReturn,
  GetVibeByVibeIdReturn,
  GetVibesReturn,
} from './models/vibes.returns';
import { VibesService } from './vibes.service';

@ApiTags('vibes')
@Controller('vibes')
export class VibesController {
  constructor(private readonly vibesService: VibesService) {}

  @ApiOperation({
    summary: 'Vibe oluşturulur',
  })
  @ApiResponse({
    status: 200,
    type: CreateVibeReturn,
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Post('create')
  async createVibe(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 50 * 1024 * 1024 }), // 50MB
          new FileTypeValidator({ fileType: /(jpg|jpeg|png|gif|mp4|mov|avi)$/ }),
        ],
      }),
    )
    file: Express.Multer.File,
    @Body() createVibeDto: CreateVibeDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.createVibe({
      sessionId,
      ...createVibeDto,
      file,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe silinir',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deleteByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.deleteByVibeId({
      sessionId,

      vibeId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe sekmesi listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetVibesReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('')
  async getVibes(
    @Res({ passthrough: true }) res: Response,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.getVibes({
      sessionId,

      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe getirilir',
  })
  @ApiResponse({
    status: 200,
    type: GetVibeByVibeIdReturn,
  })
  @Get(':id')
  async getByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.getByVibeId({
      sessionId,

      vibeId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe yorumları listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetCommentsByVibeIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':id/comments')
  async getCommentsByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.getCommentsByVibeId({
      sessionId,

      vibeId,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe bilgileri güncellenir',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update')
  async updateByVibeId(
    @Body() updateByVibeIdDto: UpdateByVibeIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.updateByVibeId({
      sessionId,

      vibeId,
      ...updateByVibeIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenileri listelenir',
  })
  @ApiResponse({
    status: 200,
    type: GetLikesByVibeIdReturn,
  })
  @Get(':id/likes')
  async getLikesByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.getLikesByVibeId({
      sessionId,

      vibeId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/likes/like')
  async likeByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.likeByVibeId({
      sessionId,

      vibeId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenisi kaldırılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/likes/unlike')
  async unlikeByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.unlikeByVibeId({
      sessionId,

      vibeId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe gizlenir',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/hide')
  async hideByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.hideByVibeId({
      sessionId,

      vibeId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe gösterime girer',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/show')
  async showByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.vibesService.showByVibeId({
      sessionId,

      vibeId,
    });
    return result;
  }
}
