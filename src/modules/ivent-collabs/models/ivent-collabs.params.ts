export type LeaveCollabrationByIventIdParams = {
  sessionId: string;
  iventId: string;
};

export type RemoveCollabByIventIdParams = {
  sessionId: string;
  iventId: string;
  collabId: string;
  collabType: string;
};

export type SearchCollabsForIventCreationParams = {
  sessionId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchCollabsParams = {
  sessionId: string;
  iventId: string;
  q: string;
  limit: number;
  page: number;
};
