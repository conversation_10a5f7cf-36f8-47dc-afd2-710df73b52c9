export type BlockPageByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type CreatePageParams = {
  sessionId: string;
  pageName: string;
  thumbnailUrl?: string | null;
  websiteUrl?: string | null;
  description?: string | null;
  isEdu: boolean;
  haveMembership: boolean;
  tagIds: string[];
  creatorIds: string[];
  locationId: string;
};

export type DeletePageByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type FollowByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type GetIventsCreatedByPageIdParams = {
  sessionId: string;
  pageId: string;
  limit: number;
  page: number;
};

export type GetPageByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type GetPageDetailsByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type GetVibeFoldersByPageIdParams = {
  sessionId: string;
  pageId: string;
  limit: number;
  page: number;
};

export type RemoveFollowerByPageIdParams = {
  sessionId: string;
  pageId: string;
  userId: string;
};

export type SearchFollowersByPageIdParams = {
  sessionId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SubscribeByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type UnblockPageByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type UnfollowByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type UnsubscribeByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type UpdateDescriptionByPageIdParams = {
  sessionId: string;
  pageId: string;
  newDescription: string;
};

export type UpdateLinksByPageIdParams = {
  sessionId: string;
  pageId: string;
  newLink: string;
};

export type UpdateLocationByPageIdParams = {
  sessionId: string;
  pageId: string;
  newLocationId: string;
};
