import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString, IsUUID, IsUrl } from 'class-validator';

export class CreatePageDto {
  @ApiProperty({
    description: 'Name of the page',
    example: 'Photography Club Istanbul',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  pageName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the page thumbnail image',
    example: 'https://example.com/page-thumbnail.jpg',
    format: 'url',
  })
  @IsUrl({}, { message: 'Thumbnail URL must be a valid URL' })
  @IsOptional()
  thumbnailUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Website URL for the page',
    example: 'https://photographyclub.com',
    format: 'url',
  })
  @IsUrl({}, { message: 'Website URL must be a valid URL' })
  @IsOptional()
  websiteUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Description of the page',
    example: 'A community for photography enthusiasts in Istanbul',
  })
  @IsString()
  @IsOptional()
  description?: string | null;

  @ApiProperty({
    description: 'Whether this page is educational',
    example: false,
  })
  @IsBoolean()
  isEdu!: boolean;

  @ApiProperty({
    description: 'Whether this page has membership functionality',
    example: true,
  })
  @IsBoolean()
  haveMembership!: boolean;

  @ApiProperty({
    format: 'uuid',
    description: 'Array of hobby tag UUIDs associated with the page',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each hobby tag ID must be a valid UUID v4' })
  tagIds!: string[];

  @ApiProperty({
    format: 'uuid',
    description: 'Array of creator user UUIDs for the page',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each creator ID must be a valid UUID v4' })
  creatorIds!: string[];

  @ApiProperty({
    description: 'UUID of the location where the page is based',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Location ID must be a valid UUID v4' })
  locationId!: string;
}

export class RemoveFollowerByPageIdDto {
  @ApiProperty({
    description: 'UUID of the user to remove as a follower',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'User ID must be a valid UUID v4' })
  userId!: string;
}

export class UpdateDescriptionByPageIdDto {
  @ApiProperty({
    description: 'New description for the page',
    example: 'Updated description with new information about our photography community',
  })
  @IsString()
  newDescription!: string;
}

export class UpdateLinksByPageIdDto {
  @ApiProperty({
    description: 'New website link for the page',
    example: 'https://newwebsite.com',
    format: 'url',
  })
  @IsUrl({}, { message: 'New link must be a valid URL' })
  newLink!: string;
}

export class UpdateLocationByPageIdDto {
  @ApiProperty({
    description: 'UUID of the new location for the page',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'New location ID must be a valid UUID v4' })
  newLocationId!: string;
}
