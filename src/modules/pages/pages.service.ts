import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import {
  BlockPageByPageIdParams,
  CreatePageParams,
  DeletePageByPageIdParams,
  FollowByPageIdParams,
  GetIventsCreatedByPageIdParams,
  GetPageByPageIdParams,
  GetPageDetailsByPageIdParams,
  GetVibeFoldersByPageIdParams,
  RemoveFollowerByPageIdParams,
  SearchFollowersByPageIdParams,
  SubscribeByPageIdParams,
  UnblockPageByPageIdParams,
  UnfollowByPageIdParams,
  UnsubscribeByPageIdParams,
  UpdateDescriptionByPageIdParams,
  UpdateLinksByPageIdParams,
  UpdateLocationByPageIdParams,
} from './models/pages.params';
import {
  CreatePageReturn,
  GetIventsCreatedByPageIdReturn,
  GetPageByPageIdReturn,
  GetPageDetailsByPageIdReturn,
  GetVibeFoldersByPageIdReturn,
  SearchFollowersByPageIdReturn,
} from './models/pages.returns';

@Injectable()
export class PagesService {
  constructor(private dataSource: DataSource) {}

  async createPage(createPageParams: CreatePageParams): Promise<CreatePageReturn> {
    const {
      sessionId,

      pageName,
      thumbnailUrl,
      websiteUrl,
      description,
      isEdu,
      haveMembership,
      tagIds,
      creatorIds,
      locationId,
    } = createPageParams;

    const pageInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'pages',
        values: {
          page_name: pageName,
          thumbnail_url: thumbnailUrl,
          website_url: websiteUrl,
          description,
          is_edu: isEdu,
          have_membership: haveMembership,
          location_id: locationId,
          creator_id: sessionId,
        },
      }),
    );
    const insertedPageId = pageInsertResult[0].id;

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'page_memberships',
        values: [
          ...creatorIds.map((val) => ({
            member_id: val,
            page_id: insertedPageId,
            status: 'moderator',
            inviter_id: sessionId,
          })),
          {
            member_id: sessionId,
            page_id: insertedPageId,
            status: 'admin',
            inviter_id: sessionId,
          },
        ],
      }),
    );

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'page_tags',
        values: tagIds.map((val) => ({
          page_id: insertedPageId,
          hobby_id: val,
        })),
      }),
    );

    return {
      pageId: insertedPageId,
    };
  }

  async deletePageByPageId(deleteByPageIdParams: DeletePageByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = deleteByPageIdParams;
    return {};
  }

  async getPageByPageId(getPageByPageIdParams: GetPageByPageIdParams): Promise<GetPageByPageIdReturn> {
    const { sessionId, pageId } = getPageByPageIdParams;

    const administrationCheckResult = await this.dataSource.query(`
      SELECT status FROM page_memberships WHERE member_id = ${sessionId} AND page_id = ${pageId} AND status IN ('admin', 'moderator');
    `);

    const pageResult = await this.dataSource.query(`
      WITH PageIventCount AS (
          -- Count of the ivents that page participated in, grouped by page
          SELECT
              COUNT(ivent_id) AS ivent_count,
              account_id
          FROM ivent_users
          WHERE status IN ('accepted', 'joined', 'admin')
          GROUP BY account_id
      ),
      PageFollowerCount AS (
          -- Count of the followers, grouped by page
          SELECT
            COUNT(user_id) AS follower_count,
            page_id
          FROM page_followers
          GROUP BY page_id
      ),
      PageTagsList AS (
          -- String as a comma seperated list of the tags that the page have, grouped by page
          SELECT
              STRING_AGG(DISTINCT h.hobby_name, ',') AS tags,
              pt.page_id AS page_id
          FROM page_tags pt
          LEFT JOIN hobbies h ON h.id = pt.hobby_id
          GROUP BY pt.page_id
      )
      SELECT
          p.id AS id,
          p.page_name AS page_name,
          p.thumbnail_url AS thumbnail_url,
          pic.ivent_count AS ivent_count,
          pfc.follower_count AS follower_count,
          ptl.tags AS tags,
          p.have_membership AS have_membership
      FROM pages p
      LEFT JOIN PageIventCount pic ON pic.account_id = p.id
      LEFT JOIN PageFollowerCount pfc ON pfc.page_id = p.id
      LEFT JOIN PageTagsList ptl ON ptl.page_id = p.id
      WHERE p.id = ${pageId};
    `);

    return {
      pageId: pageResult[0].id,
      pageName: pageResult[0].page_name,
      thumbnailUrl: pageResult[0].thumbnail_url,
      createdIventCount: pageResult[0].ivent_count ?? 0,
      followerCount: pageResult[0].follower_count ?? 0,
      tagIds: pageResult[0].tags ? pageResult[0].tags.split(',') : [],
      haveMembership: pageResult[0].have_membership,
      isFirstPerson: administrationCheckResult.length !== 0,
    };
  }

  async getIventsCreatedByPageId(
    getIventsCreatedByPageIdParams: GetIventsCreatedByPageIdParams,
  ): Promise<GetIventsCreatedByPageIdReturn> {
    const { sessionId, pageId, limit, page } = getIventsCreatedByPageIdParams;

    const iventsResult = await this.dataSource.query(`
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          'default' AS view_type,
          EXISTS (
              SELECT 1
              FROM user_favorites
              WHERE
                  user_id = '${sessionId}'
                  AND favorited_ivent_id = i.id
          ) AS is_favorited
      FROM ivent_users iu
      LEFT JOIN ivents i ON i.id = iu.ivent_id
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      WHERE iu.account_id = '${pageId}'
      AND iu.status IN ('accepted', 'admin')
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      ivents: iventsResult.map((val) => ({
        iventId: val.ivent_id,
        iventName: val.ivent_name,
        thumbnailUrl: val.thumbnail_url,
        locationName: val.location_name,
        creatorId: val.creator_id,
        creatorType: val.creator_type,
        creatorName: val.creator_name,
        creatorImageUrl: val.creator_image_url,
        isFavorited: val.is_favorited,
        viewType: 'default',
      })),
      iventCount: iventsResult.length,
    };
  }

  async getVibeFoldersByPageId(
    getVibeFoldersByPageIdParams: GetVibeFoldersByPageIdParams,
  ): Promise<GetVibeFoldersByPageIdReturn> {
    const { sessionId, pageId, limit, page } = getVibeFoldersByPageIdParams;

    const vibeFoldersResult = await this.dataSource.query(`
      WITH RecentVibes AS (
          -- Get the most recent vibe of each vibe folder
          SELECT
              v.id AS vibe_id,
              vf.vibe_folder_id AS vibe_folder_id
          FROM vibe_folder_id vf
          LEFT JOIN vibes v ON v.vibe_folder_id = vf.id
          ORDER BY v.created_at DESC
          LIMIT 1
      )
      SELECT
          vf.id vibe_folder_id,
          vf.thumbnail_url AS thumbnail_url,
          i.id AS ivent_id,
          d.date AS date,
          i.ivent_name AS ivent_name,
          csof.collab_summary AS member_summary
          rv.vibe_id AS vibe_id
      FROM ivent_users AS iu
      LEFT JOIN ivents i ON i.id = iu.ivent_id
      LEFT JOIN vibe_folders vf ON vf.id = i.vibe_folder_id
      LEFT JOIN ivent_dates_aggregated d ON d.ivent_id = i.id
      LEFT JOIN collab_summary_of_ivent csof ON csof.ivent_id = i.id
      LEFT JOIN RecentVibes rv ON rv.vibe_folder_id = vf.id
      WHERE iu.account_id = '${pageId}'
      AND iu.status IN ('accepted', 'admin')
      AND csof.account_id = '${sessionId}'
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      vibeFolders: vibeFoldersResult.map((val) => ({
        vibeFolderId: val.vibe_folder_id,
        thumbnailUrl: val.thumbnail_url,
        iventId: val.ivent_id,
        iventName: val.ivent_name,
        date: val.date,
        memberNames: val.member_summary ? val.member_summary.split('|')[2].split(',') : [],
        memberCount: val.member_summary ? Number(val.member_summary.split('|')[1]) : 0,
        vibeId: val.vibe_id,
      })),
      vibeFolderCount: vibeFoldersResult.length,
    };
  }

  async getDetailsByPageId(
    getDetailsByPageIdParams: GetPageDetailsByPageIdParams,
  ): Promise<GetPageDetailsByPageIdReturn> {
    const { sessionId, pageId } = getDetailsByPageIdParams;

    const pageResult = await this.dataSource.query(`
      SELECT
          p.page_name AS page_name,
          p.website_url AS website_url,
          l.id AS location_id,
          l.open_address AS location_address
      FROM pages p
      LEFT JOIN locations l ON l.id = p.location_id
      WHERE p.id = ${pageId};
    `);

    return {
      description: pageResult[0].description,
      websiteUrl: pageResult[0].website_url,
      locationId: pageResult[0].location_id,
      locationAdress: pageResult[0].location_address,
    };
  }

  async updateDescriptionByPageId(
    updateDescriptionByPageIdParams: UpdateDescriptionByPageIdParams,
  ): Promise<EmptyReturn> {
    const { ...rest } = updateDescriptionByPageIdParams;
    return {};
  }

  async updateLinksByPageId(updateLinksByPageIdParams: UpdateLinksByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = updateLinksByPageIdParams;
    return {};
  }

  async updateLocationByPageId(updateLocationByPageIdParams: UpdateLocationByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = updateLocationByPageIdParams;
    return {};
  }

  async blockPageByPageId(blockPageByPageIdParams: BlockPageByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = blockPageByPageIdParams;
    return {};
  }

  async unblockPageByPageId(unblockPageByPageIdParams: UnblockPageByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = unblockPageByPageIdParams;
    return {};
  }

  async subscribeByPageId(subscribeByPageIdParams: SubscribeByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = subscribeByPageIdParams;
    return {};
  }

  async unsubscribeByPageId(unsubscribeByPageIdParams: UnsubscribeByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = unsubscribeByPageIdParams;
    return {};
  }

  async searchFollowersByPageId(
    searchFollowersByPageIdParams: SearchFollowersByPageIdParams,
  ): Promise<SearchFollowersByPageIdReturn> {
    const { sessionId, pageId, q, limit, page } = searchFollowersByPageIdParams;

    const administrationCheckResult = await this.dataSource.query(`
      SELECT status FROM page_memberships WHERE member_id = ${sessionId} AND page_id = ${pageId} AND status IN ('admin', 'moderator');
    `);
    if (administrationCheckResult.length === 0) {
      throw new HttpException('You are not authorized to view this page.', HttpStatus.BAD_REQUEST);
    }

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const followersResult = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM page_followers pf
      LEFT JOIN users u ON u.id = pf.user_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE pf.page_id = '${pageId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: followersResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      userCount: followersResult.length,
    };
  }

  async followByPageId(followByPageIdParams: FollowByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = followByPageIdParams;
    return {};
  }

  async unfollowByPageId(unfollowByPageIdParams: UnfollowByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = unfollowByPageIdParams;
    return {};
  }

  async removeFollowerByPageId(removeFollowerByPageIdParams: RemoveFollowerByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = removeFollowerByPageIdParams;
    return {};
  }
}
