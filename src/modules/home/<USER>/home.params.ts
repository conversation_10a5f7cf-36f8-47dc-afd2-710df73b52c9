import { FeedDateEnum } from 'src/constants/enums';

export type FeedParams = {
  sessionId: string;
  dateType: FeedDateEnum;
  categories: string[];
  startDate: string | null;
  endDate: string | null;
  locationCoeff: number;
  latitude: number | null;
  longitude: number | null;
  q: string;
  limit: number;
  page: number;
};

export type MapParams = {
  sessionId: string;
  startDate: string;
  endDate: string;
  latStart: number;
  latEnd: number;
  lngStart: number;
  lngEnd: number;
  limit: number;
};

export type SearchAccountParams = {
  sessionId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchIventParams = {
  sessionId: string;
  q: string;
  limit: number;
  page: number;
};
