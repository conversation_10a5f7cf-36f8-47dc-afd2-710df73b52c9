import { Controller, DefaultValuePipe, Get, ParseFloatPipe, ParseIntPipe, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { FeedDateEnum } from 'src/constants/enums/feed-date-enum';
import { NullableParseFloatPipe } from 'src/pipes/nullable-parse-float.pipe';
import { HomeService } from './home.service';
import { FeedReturn, MapReturn, SearchAccountReturn, SearchIventReturn } from './models/home.returns';

@ApiTags('home')
@Controller('')
export class HomeController {
  constructor(private readonly homeService: HomeService) {}

  @ApiOperation({
    summary: 'Feedi listeler',
    description: 'Şu anda feedi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.',
  })
  @ApiResponse({
    status: 200,
    type: FeedReturn,
  })
  @ApiQuery({
    name: 'dateType',
    enum: FeedDateEnum,
    enumName: 'FeedDateEnum',
    description: 'Date range to filter the feed',
    example: FeedDateEnum.TODAY,
  })
  @ApiQuery({
    name: 'startDate',
    type: 'string',
    example: '2021-12-31',
    description: 'Start date for the feed',
    required: false,
  })
  @ApiQuery({
    name: 'endDate',
    type: 'string',
    example: '2021-12-31',
    description: 'End date for the feed',
    required: false,
  })
  @ApiQuery({ name: 'locationCoeff', required: false, type: 'integer' })
  @ApiQuery({ name: 'latitude', required: false, type: 'double', example: 40.7766 })
  @ApiQuery({ name: 'longitude', required: false, type: 'double', example: -73.9712 })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('feed')
  async feed(
    @Res({ passthrough: true }) res: Response,
    @Query('dateType') dateType: FeedDateEnum,
    @Query('categories') categories: string,
    @Query('startDate', new DefaultValuePipe(null)) startDate: string | null,
    @Query('endDate', new DefaultValuePipe(null)) endDate: string | null,
    @Query('locationCoeff', new DefaultValuePipe(5), ParseIntPipe) locationCoeff: number,
    @Query('latitude', NullableParseFloatPipe) latitude: number | null,
    @Query('longitude', NullableParseFloatPipe) longitude: number | null,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.homeService.feed({
      sessionId,

      dateType,
      categories: categories.split(',').filter((x) => x.trim() !== ''),
      startDate,
      endDate,
      locationCoeff,
      latitude,
      longitude,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Mapi listeler',
    description: 'Şu anda mapi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.',
  })
  @ApiResponse({
    status: 200,
    type: MapReturn,
  })
  @ApiQuery({
    name: 'startDate',
    type: 'string',
    example: '2021-12-31',
    description: 'Start date for the ivents on map',
  })
  @ApiQuery({
    name: 'endDate',
    type: 'string',
    example: '2021-12-31',
    description: 'End date for the ivents on map',
  })
  @ApiQuery({ name: 'latStart', type: 'double', example: 40.7766 })
  @ApiQuery({ name: 'latEnd', type: 'double', example: 40.7766 })
  @ApiQuery({ name: 'lngStart', type: 'double', example: -73.9712 })
  @ApiQuery({ name: 'lngEnd', type: 'double', example: -73.9712 })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @Get('map')
  async map(
    @Res({ passthrough: true }) res: Response,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('latStart', ParseFloatPipe) latStart: number,
    @Query('latEnd', ParseFloatPipe) latEnd: number,
    @Query('lngStart', ParseFloatPipe) lngStart: number,
    @Query('lngEnd', ParseFloatPipe) lngEnd: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.homeService.map({
      sessionId,

      startDate,
      endDate,
      latStart,
      latEnd,
      lngStart,
      lngEnd,
      limit,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Uygulamada arama yapar',
  })
  @ApiResponse({
    status: 200,
    type: SearchIventReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('searchIvent')
  async searchIvent(
    @Res({ passthrough: true }) res: Response,
    @Query('q') q: string,
    @Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.homeService.searchIvent({
      sessionId,

      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Uygulamada arama yapar',
  })
  @ApiResponse({
    status: 200,
    type: SearchAccountReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('searchAccount')
  async searchAccount(
    @Res({ passthrough: true }) res: Response,
    @Query('q') q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.homeService.searchAccount({
      sessionId,

      q,
      limit,
      page,
    });
    return result;
  }
}
