import { Injectable } from '@nestjs/common';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { CreateSquadParams } from './models/squads.params';

@Injectable()
export class SquadsService {
  constructor(private dataSource: DataSource) {}

  async createSquad(createSquadParams: CreateSquadParams): Promise<string> {
    const { creatorId, iventId } = createSquadParams;

    const vibeFolderInsertResult = await this.dataSource.query(
      insertQueryBuilder({ tableName: 'vibe_folders', values: {} }),
    );
    const memoryFolderInsertResult = await this.dataSource.query(
      insertQueryBuilder({ tableName: 'memory_folders', values: {} }),
    );
    const squadInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'squads',
        values: {
          ivent_id: iventId,
          creator_id: creatorId,
          vibe_folder_id: vibeFolderInsertResult[0].id,
          memory_folder_id: memoryFolderInsertResult[0].id,
        },
      }),
    );

    return squadInsertResult[0].id;
  }
}
