import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { BlockUserByPageIdDto, UnblockUserByPageIdDto } from './models/page-blacklists.dto';
import { SearchPageBlocklistByPageIdReturn } from './models/page-blacklists.returns';
import { PageBlacklistsService } from './page-blacklists.service';

@ApiTags('pageBlacklists')
@Controller('pageBlacklists')
export class PageBlacklistsController {
  constructor(private readonly pageBlacklistsService: PageBlacklistsService) {}

  @ApiOperation({
    summary: 'Sayfa IDsi ile engellenenleri listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchPageBlocklistByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/blocklist')
  async searchPageBlocklistByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.pageBlacklistsService.searchPageBlocklistByPageId({
      sessionId,

      pageId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile bir hesabı engeller',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/block')
  async blockUserByPageId(
    @Body() blockUserByPageIdDto: BlockUserByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.pageBlacklistsService.blockUserByPageId({
      sessionId,

      pageId,
      ...blockUserByPageIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile bir hesabın engelini kaldırır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/unblock')
  async unblockUserByPageId(
    @Body() unblockUserByPageIdDto: UnblockUserByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.pageBlacklistsService.unblockUserByPageId({
      sessionId,

      pageId,
      ...unblockUserByPageIdDto,
    });
    return result;
  }
}
