import { Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { DataSource } from 'typeorm';
import {
  BlockUserByPageIdParams,
  SearchPageBlocklistByPageIdParams,
  UnblockUserByPageIdParams,
} from './models/page-blacklists.params';
import { SearchPageBlocklistByPageIdReturn } from './models/page-blacklists.returns';

@Injectable()
export class PageBlacklistsService {
  constructor(private dataSource: DataSource) {}

  async searchPageBlocklistByPageId(
    searchPageBlocklistByPageIdParams: SearchPageBlocklistByPageIdParams,
  ): Promise<SearchPageBlocklistByPageIdReturn> {
    const { ...rest } = searchPageBlocklistByPageIdParams;
    return {
      users: [],
      userCount: 0,
    };
  }

  async blockUserByPageId(blockUserByPageIdParams: BlockUserByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = blockUserByPageIdParams;
    return {};
  }

  async unblockUserByPageId(unblockUserByPageIdParams: UnblockUserByPageIdParams): Promise<EmptyReturn> {
    const { ...rest } = unblockUserByPageIdParams;
    return {};
  }
}
