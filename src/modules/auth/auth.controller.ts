import { Body, Controller, Param, ParseUUIDPipe, Post, Res } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { SendVerificationCodeDto, ValidateDto } from './models/auth.dto';
import { ValidateReturn } from './models/auth.returns';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({
    summary: 'Hesaptan çıkış yapar',
  })
  @ApiResponse({ status: 200 })
  @Post('logout')
  async logout(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.authService.logout({
      sessionId,

      userId,
    });
    res.clearCookie('auth-token');
    return result;
  }

  @ApiOperation({
    summary:
      'Telefon numarasına gönderilmiş onay kodunu doğrular ve telefon numarası yeniyse onu kayıt olma sayfasına, değilse giriş sayfasına yönlendirir',
  })
  @ApiResponse({
    status: 200,
    type: ValidateReturn,
  })
  @Post('validate')
  async validate(@Body() validateDto: ValidateDto) {
    const result = await this.authService.validate({
      ...validateDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Telefon numarasına onay kodu gönderir',
  })
  @ApiResponse({ status: 200 })
  @Post('send-verification-code')
  async sendVerificationCode(@Body() sendVerificationCodeDto: SendVerificationCodeDto) {
    const result = await this.authService.sendVerificationCode({
      ...sendVerificationCodeDto,
    });
    return result;
  }
}
