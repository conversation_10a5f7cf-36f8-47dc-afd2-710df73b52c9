import { ApiProperty } from '@nestjs/swagger';
import { AuthEnum } from 'src/constants/enums/auth-enum';
import { UserRoleEnum } from 'src/entities';

export class ValidateReturn {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'JWT authentication token for authenticated users',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Unique identifier of the authenticated user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId?: string | null;

  @ApiProperty({
    enum: UserRoleEnum,
    enumName: 'UserRoleEnum',
    description: 'User role in the system',
    example: UserRoleEnum.LEVEL_0,
    nullable: true,
    required: false,
  })
  role?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Username of the authenticated user',
    example: 'john_doe',
  })
  username?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Full name of the authenticated user',
    example: 'John Doe',
  })
  fullname?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl?: string | null;

  @ApiProperty({
    enum: AuthEnum,
    enumName: 'AuthEnum',
    description: 'Authentication result type',
    example: AuthEnum.LOGIN,
  })
  type!: AuthEnum;
}
