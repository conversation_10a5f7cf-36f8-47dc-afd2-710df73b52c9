import { Controller, DefaultValuePipe, ParseIntPipe, Post, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { SearchUniversitiesReturn } from './models/universities.returns';
import { UniversitiesService } from './universities.service';

@ApiTags('universities')
@Controller('universities')
export class UniversitiesController {
  constructor(private readonly universitiesService: UniversitiesService) {}

  @ApiOperation({
    summary: 'Ivent oluşturulurken üniversiteleri listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchUniversitiesReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Post('search')
  async searchUniversities(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.universitiesService.searchUniversities({
      sessionId,

      q,
      limit,
      page,
    });
    return result;
  }
}
