import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { FriendListingTypeEnum } from 'src/constants/enums';
import { InviteFriendsByIventIdDto, JoinIventAndCreateSquadByIventIdDto } from './models/squad-memberships.dto';
import {
  SearchInvitableUsersByIventIdReturn,
  SearchParticipantsByIventIdReturn,
} from './models/squad-memberships.returns';
import { SquadMembershipsService } from './squad-memberships.service';

@ApiTags('squadMemberships')
@Controller('squadMemberships')
export class SquadMembershipsController {
  constructor(private readonly squadMembershipsService: SquadMembershipsService) {}

  @ApiOperation({
    summary: 'Ivente katılırken davet edilebilecek hesapları listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchInvitableUsersByIventIdReturn,
  })
  @ApiQuery({
    name: 'type',

    enum: FriendListingTypeEnum,
    enumName: 'FriendListingTypeEnum',
    description: 'Type of friends to list - either groups or users',
    example: FriendListingTypeEnum.USER,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':iventId/search')
  async searchInvitableUsersByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
    @Query('type') type: FriendListingTypeEnum,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.squadMembershipsService.searchInvitableUsersByIventId({
      sessionId,

      iventId,
      type,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary:
      'Ivent IDsi ile (eğer iventte yetkiliyse): ivente katılan bütün hesapları, (eğer ivente katılmayan bir user ise): ivente katılan ya da favorilemiş arkadaşları, (eğer ivente katılan bir user ise): ivente birlikte katıldığı arkadaşları listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchParticipantsByIventIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':iventId')
  async searchParticipantsByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.squadMembershipsService.searchParticipantsByIventId({
      sessionId,

      iventId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile seçilen hesaplarla birlikte ivente katılır (seçili hesaplara davet gönderilir)',
  })
  @ApiResponse({ status: 200 })
  @Post(':iventId/join')
  async joinIventAndCreateSquadByIventId(
    @Body()
    joinIventAndCreateSquadByIventIdDto: JoinIventAndCreateSquadByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.squadMembershipsService.joinIventAndCreateSquadByIventId({
      sessionId,

      iventId,
      ...joinIventAndCreateSquadByIventIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile etkinlikten ayrılınır',
  })
  @ApiResponse({ status: 200 })
  @Post(':iventId/leave')
  async leaveSquadByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.squadMembershipsService.leaveSquadByIventId({
      sessionId,

      iventId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile seçilen hesaplar davet edilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':iventId/invite')
  async inviteFriendsByIventId(
    @Body()
    inviteFriendsByIventIdDto: InviteFriendsByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.squadMembershipsService.inviteFriendsByIventId({
      sessionId,

      iventId,
      ...inviteFriendsByIventIdDto,
    });
    return result;
  }
}
