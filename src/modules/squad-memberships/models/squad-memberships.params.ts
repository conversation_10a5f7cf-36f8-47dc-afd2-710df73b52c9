import { FriendListingTypeEnum } from 'src/constants/enums';

export type InviteFriendsByIventId = {
  sessionId: string;
  iventId: string;
  groupIds: string[];
  userIds: string[];
};

export type JoinIventAndCreateSquadByIventIdParams = {
  sessionId: string;
  iventId: string;
  groupIds: string[];
  userIds: string[];
};

export type LeaveSquadByIventId = {
  sessionId: string;
  iventId: string;
};

export type SearchInvitableUsersByIventIdParams = {
  sessionId: string;
  iventId: string;
  type: FriendListingTypeEnum;
  q: string;
  limit: number;
  page: number;
};

export type SearchParticipantsByIventIdParams = {
  sessionId: string;
  iventId: string;
  q: string;
  limit: number;
  page: number;
};
