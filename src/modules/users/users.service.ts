import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { sign } from 'jsonwebtoken';
import { IventListingTypeEnum } from 'src/constants/enums';
import { Ivent, IventUserStatusEnum, User, UserFavorite, UserRoleEnum } from 'src/entities';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import {
  DeleteUserByUserIdParams,
  FollowByUserIdParams,
  GetContactsByUserIdParams,
  GetFavoritesByUserIdParams,
  GetFollowerFriendsByUserIdParams,
  GetFollowersByUserIdParams,
  GetFollowingsByUserIdParams,
  GetIventsByUserIdParams,
  GetLevelByUserIdParams,
  GetMemoryFoldersByUserIdParams,
  GetPagesByUserIdParams,
  GetUserBannerByUserIdParams,
  GetUserByUserIdParams,
  GetVibeFoldersByUserIdParams,
  RegisterParams,
  RemoveFollowerByUserIdParams,
  SendCreatorRequestFormParams,
  SendVerificationEmailParams,
  SubscribeByUserIdParams,
  UnfollowByUserIdParams,
  UnsubscribeByUserIdParams,
  UpdateByUserIdParams,
  UpdateEmailByUserIdParams,
  UpdateGradByUserIdParams,
  UpdateNotificationsByUserIdParams,
  UpdatePhoneNumberByUserIdParams,
  UpdatePrivacyByUserIdParams,
  ValidateEmailParams,
} from './models/users.params';
import {
  GetContactsByUserIdReturn,
  GetFavoritesByUserIdReturn,
  GetFollowerFriendsByUserIdReturn,
  GetFollowersByUserIdReturn,
  GetFollowingsByUserIdReturn,
  GetIventsByUserIdReturn,
  GetLevelByUserIdReturn,
  GetMemoryFoldersByUserIdReturn,
  GetPagesByUserIdReturn,
  GetUserBannerByUserIdReturn,
  GetUserByUserIdReturn,
  GetVibeFoldersByUserIdReturn,
  RegisterReturn,
} from './models/users.returns';

@Injectable()
export class UsersService {
  constructor(private dataSource: DataSource) {}

  async register(params: RegisterParams): Promise<RegisterReturn> {
    // Initialize
    const { phoneNumber, fullname, hobbyIds } = params;

    // Split name
    // TODO: Check the length of fullname
    const nameArray = fullname.replace(/\s+/g, ' ').split(' ');
    const lastname = nameArray.pop();
    const firstname = nameArray.join(' ');

    // Standardize characters to English letters
    const standardizeToEnglish = (text: string): string => {
      const charMap: { [key: string]: string } = {
        // Turkish characters
        ç: 'c',
        Ç: 'C',
        ğ: 'g',
        Ğ: 'G',
        ı: 'i',
        I: 'I',
        İ: 'I',
        i: 'i',
        ö: 'o',
        Ö: 'O',
        ş: 's',
        Ş: 'S',
        ü: 'u',
        Ü: 'U',
      };

      return text.replace(/[^\w\s.-]/g, (char) => charMap[char] || char);
    };

    // Find unique username
    let usernameRoot = standardizeToEnglish(`${firstname}.${lastname}`).toLowerCase();
    let username = usernameRoot;
    const existingUsernames = await this.dataSource.query(`
        SELECT username
        FROM users
        WHERE username LIKE '${username}%';
    `);
    while (true) {
      const condition1 = !existingUsernames.some((val) => val.username === username);
      const condition2 = username.length > 3 && username.length < 30;
      if (condition1 && condition2) break;
      username = `${usernameRoot}${Math.floor(Math.random() * 1000)}`;
    }

    // Insert user
    const userInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'users',
        values: {
          phone_number: phoneNumber,
          firstname,
          lastname,
          username,
        },
      }),
    );
    const insertedUserId = userInsertResult[0].id;

    // Insert user hobbies
    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_hobbies',
        values: hobbyIds.map((val) => ({
          user_id: insertedUserId,
          hobby_id: val,
        })),
      }),
    );

    // Extra

    // Save and return token, role and userId
    const token = sign({ _id: insertedUserId, role: 'level_0' }, process.env.JWT_SECRET as string, {
      expiresIn: '7d',
    });
    return {
      userId: insertedUserId,
      token,
      role: UserRoleEnum.LEVEL_0,
      username,
      fullname: `${firstname} ${lastname}`,
    };
  }

  async deleteByUserId(deleteByUserIdParams: DeleteUserByUserIdParams): Promise<EmptyReturn> {
    // TODO: LOOK
    const { sessionId, userId } = deleteByUserIdParams;
    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);
    await this.dataSource.createQueryBuilder().delete().from('users').where({ id: userId }).execute();
    return {};
  }

  async getByUserId(params: GetUserByUserIdParams): Promise<GetUserByUserIdReturn> {
    const { sessionId, userId } = params;

    const isFirstPerson = sessionId === userId;

    const query = `
      WITH UserIventStats AS (
          SELECT COUNT(ivent_id) AS ivent_count, account_id
          FROM ivent_users
          WHERE status IN ('accepted', 'joined', 'admin')
          GROUP BY account_id
      ),
      UserFriendStats AS (
          SELECT COUNT(friend_id) AS friend_count, user_id
          FROM user_friendships
          WHERE status = 'accepted'
          GROUP BY user_id
      ),
      UserFollowerStats AS (
          SELECT COUNT(follower_id) AS follower_count, following_id
          FROM user_followers
          GROUP BY following_id
      ),
      UserFollowingStats AS (
          SELECT COUNT(following_id) AS following_count, follower_id
          FROM user_followers
          GROUP BY follower_id
      ),
      UserHobbyList AS (
          SELECT STRING_AGG(DISTINCT h.hobby_name, ',') AS hobbies, uh.user_id
          FROM user_hobbies uh
          LEFT JOIN hobbies h ON h.id = uh.hobby_id
          GROUP BY uh.user_id
      )
      SELECT 
          u.id AS user_id, u.role AS user_role, u.username, 
          CONCAT(u.firstname, ' ', u.lastname) AS fullname, u.avatar_url,
          COALESCE(uis.ivent_count, 0) AS ivent_count,
          COALESCE(ufrs.friend_count, 0) AS friend_count,
          COALESCE(ufos.follower_count, 0) AS follower_count,
          COALESCE(ufgos.following_count, 0) AS following_count,
          COALESCE(uhl.hobbies, '') AS hobbies,
          EXISTS (
              SELECT 1 
              FROM user_followers 
              WHERE follower_id = $1 AND following_id = u.id
          ) AS is_following,
          (
            SELECT status
              FROM user_friendships 
              WHERE user_id = $1 AND friend_id = u.id
          ) AS relationship_status
      FROM users u
      LEFT JOIN UserIventStats uis ON uis.account_id = u.id
      LEFT JOIN UserFriendStats ufrs ON ufrs.user_id = u.id
      LEFT JOIN UserFollowerStats ufos ON ufos.following_id = u.id
      LEFT JOIN UserFollowingStats ufgos ON ufgos.follower_id = u.id
      LEFT JOIN UserHobbyList uhl ON uhl.user_id = u.id
      WHERE u.id = $2;`;

    const [userProfile] = await this.dataSource.query(query, [sessionId, userId]);
    if (!userProfile) throw new HttpException('NOT_FOUND', HttpStatus.NOT_FOUND);

    return {
      userId: userProfile.user_id,
      userRole: userProfile.user_role,
      username: userProfile.username,
      fullname: userProfile.fullname,
      avatarUrl: userProfile.avatar_url,
      iventCount: parseInt(userProfile.ivent_count),
      friendCount: parseInt(userProfile.friend_count),
      followerCount: parseInt(userProfile.follower_count),
      followingCount: parseInt(userProfile.following_count),
      hobbies: userProfile.hobbies ? userProfile.hobbies.split(',') : [],
      isFollowing: !!userProfile.is_following,
      isFirstPerson,
      relationshipStatus: userProfile.relationship_status,
    };
  }

  async getContactsByUserId(params: GetContactsByUserIdParams): Promise<GetContactsByUserIdReturn> {
    const { phoneNumbers, sessionId, userId, limit, page } = params;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const query = `
      SELECT u.id AS user_id, u.username, u.phone_number,
          u.avatar_url, u.university_code AS university
      FROM users u
      WHERE u.phone_number IN (${phoneNumbers.map((val) => `'${val}'`).join(',')});
    `;

    const queryResult = await this.dataSource.query(query);

    return {
      contacts: queryResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        phoneNumber: val.phone_number,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      contactCount: queryResult.length,
    };
  }

  async getFavoritesByUserId(params: GetFavoritesByUserIdParams): Promise<GetFavoritesByUserIdReturn> {
    const { sessionId, userId, q, limit, page } = params;

    const isFirstPerson = sessionId === userId;

    const queryBuilder = this.dataSource
      .getRepository(UserFavorite)
      .createQueryBuilder('uf')
      .leftJoinAndSelect('uf.favorited_ivent', 'i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoinAndSelect('i.ivent_dates_aggregated', 'd')
      .leftJoinAndSelect('i.active_session_ivents', 'asi', 'asi.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.member_summary_of_session_squad', 'msoss', 'msoss.user_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.collab_summary_of_ivent', 'csof', 'csof.account_id = :sessionId', { sessionId })
      .where('uf.user_id = :userId', { userId });

    if (q) {
      queryBuilder.andWhere('i.ivent_name ILIKE :q', { q: `${q}%` });
    }

    const ivents = await queryBuilder
      .orderBy('i.created_at', 'DESC')
      .skip(limit * (page - 1))
      .take(limit)
      .getMany();

    return {
      ivents: ivents.map((favorite: UserFavorite) => {
        const ivent = favorite.favorited_ivent!;
        return {
          iventId: ivent.id,
          iventName: ivent.ivent_name,
          thumbnailUrl: ivent.thumbnail_url,
          locationName: ivent.location!.location_name,
          dates: ivent.date_list,
          creatorId: ivent.creator_id,
          creatorType: ivent.creator_type,
          creatorUsername: ivent.creator_name,
          creatorImageUrl: ivent.creator_image_url,
          viewType: ivent.view_type,
          vibeFolderId: ivent.vibe_folder_id,
          isFavorited: true,
          memberCount: ivent.member_count,
          memberFirstnames: ivent.member_first_names,
          memberAvatarUrls: ivent.member_avatar_urls,
        };
      }),
      iventCount: ivents.length,
    };
  }

  async getFollowingsByUserId(params: GetFollowingsByUserIdParams): Promise<GetFollowingsByUserIdReturn> {
    const { sessionId, userId, q, limit, page } = params;

    const isFirstPerson = sessionId === userId;

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const query = `
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_followers uf
      LEFT JOIN users u ON uf.following_id = u.id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE uf.follower_id = '${userId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `;

    const queryResult = await this.dataSource.query(query);

    return {
      followings: queryResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      followingCount: queryResult.length,
    };
  }

  async getIventsByUserId(params: GetIventsByUserIdParams): Promise<GetIventsByUserIdReturn> {
    const { sessionId, userId, type, q, limit, page } = params;

    const isFirstPerson = sessionId === userId;

    const queryBuilder = await this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoinAndSelect('i.ivent_dates_aggregated', 'd')
      .leftJoinAndSelect('i.active_session_ivents', 'asi', 'asi.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.member_summary_of_session_squad', 'msoss', 'msoss.user_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.participant_summary_of_ivent', 'psoi', 'psoi.ivent_id = i.id')
      .innerJoin('i.ivent_users', 'iu', 'iu.account_id = :userId', { userId })
      .where('iu.type = :type', { type: type === IventListingTypeEnum.JOINED ? 'member' : 'user' })
      .andWhere('iu.status IN (:...status)', {
        status: [IventUserStatusEnum.ACCEPTED, IventUserStatusEnum.ADMIN, IventUserStatusEnum.JOINED],
      });

    // Add search query filter
    if (q && q.trim()) {
      queryBuilder.andWhere('(i.ivent_name ILIKE :q OR l.location_name ILIKE :q)', {
        q: `${q.trim()}%`,
      });
    }

    // Add pagination and ordering
    queryBuilder
      .orderBy('i.created_at', 'DESC')
      .limit(limit)
      .offset(limit * (page - 1));

    const ivents = await queryBuilder.getMany();

    return {
      ivents: ivents.map((ivent: Ivent) => ({
        vibeFolderId: ivent.vibe_folder_id,
        iventId: ivent.id,
        iventName: ivent.ivent_name,
        thumbnailUrl: ivent.thumbnail_url,
        locationName: ivent.location_name,
        dates: ivent.date_list,
        creatorId: ivent.creator_id,
        creatorType: ivent.creator_type,
        creatorUsername: ivent.creator_name,
        creatorImageUrl: ivent.creator_image_url,
        memberCount: ivent.member_count,
        memberFirstnames: ivent.member_first_names,
        memberAvatarUrls: ivent.member_avatar_urls,
        viewType: ivent.view_type,
      })),
      iventCount: ivents.length,
      isFirstPerson,
    };
  }

  async getLevelByUserId(getLevelByUserIdParams: GetLevelByUserIdParams): Promise<GetLevelByUserIdReturn> {
    const { sessionId, userId } = getLevelByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const levelResult = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.role AS level_info
      FROM users u
      WHERE u.id = '${userId}';
    `);

    return {
      levelInfo: levelResult[0].level_info,
    };
  }

  async getPagesByUserId(getPagesByUserIdParams: GetPagesByUserIdParams): Promise<GetPagesByUserIdReturn> {
    const { sessionId, userId } = getPagesByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const pagesResult = await this.dataSource.query(`
      SELECT
          p.id AS page_id,
          pm.status AS page_role,
          p.page_name AS page_name,
          p.thumbnail_url AS thumbnail_url
      FROM page_memberships pm
      LEFT JOIN pages p ON p.id = pm.page_id
      WHERE pm.member_id = '${userId}'
      AND pm.status IN ('admin', 'moderator');
    `);

    return {
      pages: pagesResult.map((val) => ({
        pageId: val.page_id,
        pageRole: val.page_role,
        pageName: val.page_name,
        thumbnailUrl: val.thumbnail_url,
      })),
      pageCount: pagesResult.length,
    };
  }

  async getMemoryFoldersByUserId(getMemoryFoldersByUserIdParams: GetMemoryFoldersByUserIdParams): Promise<GetMemoryFoldersByUserIdReturn> {
    const { sessionId, userId, limit, page } = getMemoryFoldersByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    return {
      memoryFolders: [
        {
          memoryFolderId: '1',
          thumbnailUrl: 'https://picsum.photos/200',
          iventId: '1',
          iventName: 'Ivent Name 1',
          dates: ['2021-01-01'],
          memberFirstnames: ['Member 1', 'Member 2'],
          memberCount: 2,
          createdAt: null,
        },
        {
          memoryFolderId: '2',
          thumbnailUrl: 'https://picsum.photos/200',
          iventId: '2',
          iventName: 'Ivent Name 2',
          dates: ['2021-01-01'],
          memberFirstnames: ['Member 1', 'Member 2'],
          memberCount: 2,
          createdAt: null,
        },
      ],
      memoryFolderCount: 2,
    };
  }

  async getVibeFoldersByUserId(params: GetVibeFoldersByUserIdParams): Promise<GetVibeFoldersByUserIdReturn> {
    const { sessionId, userId, limit, page } = params;

    const query = `
      WITH
          RecentVibes AS (
              SELECT DISTINCT
                  ON (vf.id) vf.id AS vibe_folder_id,
                  v.id AS vibe_id,
                  v.created_at AS created_at
              FROM vibe_folders vf
                  LEFT JOIN vibes v ON v.vibe_folder_id = vf.id
              ORDER BY vf.id, v.created_at ASC
          )
      SELECT
          vf.id AS vibe_folder_id,
          vf.thumbnail_url AS thumbnail_url,
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          msoss.member_count AS member_count,
          msoss.member_first_names AS member_first_names,
          msoss.member_avatar_urls AS member_avatar_urls,
          rv.vibe_id AS vibe_id
      FROM
          squad_memberships AS sm
          LEFT JOIN squads s ON s.id = sm.squad_id
          LEFT JOIN ivents i ON i.id = s.ivent_id
          LEFT JOIN vibe_folders vf ON vf.id = s.vibe_folder_id
          LEFT JOIN member_summary_of_session_squad msoss ON msoss.ivent_id = i.id
          LEFT JOIN RecentVibes rv ON rv.vibe_folder_id = vf.id
      WHERE sm.member_id = $1
      AND sm.status IN ('accepted', 'joined')
      AND msoss.user_id = $1
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `;

    const queryResult = await this.dataSource.query(query, [userId]);
    return {
      vibeFolders: queryResult
        .filter((row) => row.vibe_id != null)
        .map((row) => ({
          vibeFolderId: row.vibe_folder_id,
          thumbnailUrl: row.thumbnail_url,
          iventId: row.ivent_id,
          iventName: row.ivent_name,
          memberNames: row.member_summary ? row.member_summary.split('|')[0].split(',') : [],
          memberCount: row.member_summary ? Number(row.member_summary.split('|')[1]) : 0,
          vibeId: row.vibe_id,
        })),
      vibeFolderCount: queryResult.length,
    };
  }

  async updateByUserId(updateByUserIdParams: UpdateByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId, newUsername, newBirthday, newGender, newAvatarUrl } = updateByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .update('users')
      .set({
        username: newUsername,
        birthday: newBirthday,
        gender: newGender,
        avatar_url: newAvatarUrl,
      })
      .where({ id: userId })
      .execute();

    return {};
  }

  async updateEmailByUserId(updateEmailByUserIdParams: UpdateEmailByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId, newEmail } = updateEmailByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource.getRepository(User).update({ id: userId }, { email: newEmail });
    return {};
  }

  async updateGradByUserId(updateGradByUserIdParams: UpdateGradByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId, newGrad } = updateGradByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .update('users')
      .set({
        edu_verification: newGrad,
      })
      .where({ id: userId })
      .execute();

    return {};
  }

  async updateNotificationsByUserId(updateNotificationsByUserIdParams: UpdateNotificationsByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = updateNotificationsByUserIdParams; // TODO ikiye ayıracağız
    return {};
  }

  async updatePhoneNumberByUserId(updatePhoneNumberByUserIdParams: UpdatePhoneNumberByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId, newPhoneNumber } = updatePhoneNumberByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .update('users')
      .set({
        phone_number: newPhoneNumber,
      })
      .where({ id: userId })
      .execute();

    return {};
  }

  async updatePrivacyByUserId(updatePrivacyByUserIdParams: UpdatePrivacyByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = updatePrivacyByUserIdParams; // TODO ikiye ayıracağız
    return {};
  }

  async getFollowersByUserId(getFollowersByUserIdParams: GetFollowersByUserIdParams): Promise<GetFollowersByUserIdReturn> {
    const { sessionId, userId, q, limit, page } = getFollowersByUserIdParams;

    const isFirstPerson = sessionId === userId;

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const friendsResult = q
      ? null
      : await this.dataSource.query(`
      SELECT
          ARRAY_TO_STRING((ARRAY_AGG(u.firstname))[:3], ',') AS friend_firstnames,
          COUNT(u.id) AS friend_count
      FROM user_followers ufo
      INNER JOIN user_friendships ufr ON ufr.friend_id = ufo.follower_id
      LEFT JOIN users u ON u.id = ufr.friend_id
      WHERE ufr.user_id = '${sessionId}'
      AND ufo.following_id = '${userId}'
      GROUP BY u.id
    `);

    const followersResult = await this.dataSource.query(`
      WITH FriendshipsOfSessionUser AS (
          -- Record of whether the session user is friend with the user listed
          SELECT
              COUNT(friend_id) AS is_friend,
              friend_id
          FROM user_friendships
          WHERE user_id = '${sessionId}'
          GROUP BY friend_id
      )
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university,
          fosu.is_friend AS is_friend
      FROM user_followers uf
      LEFT JOIN users u ON u.id = uf.follower_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      LEFT JOIN FriendshipsOfSessionUser fosu ON fosu.friend_id = u.id
      WHERE uf.following_id = '${userId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    const defaultReturn = {
      followers: followersResult.map((val) => {
        if (isFirstPerson) {
          return {
            userId: val.user_id,
            username: val.username,
            avatarUrl: val.avatar_url,
            university: val.university,
          };
        } else {
          return {
            userId: val.user_id,
            username: val.username,
            avatarUrl: val.avatar_url,
            university: val.university,
            isFriend: val.is_friend ? true : false,
          };
        }
      }),
      followerCount: followersResult.length,
      isFirstPerson,
    };

    if (q) {
      return {
        ...defaultReturn,
        friendUsernames: [],
        friendCount: 0,
      };
    } else {
      return {
        ...defaultReturn,
        friendUsernames: friendsResult.length ? friendsResult[0].friend_firstnames.split(',') : [],
        friendCount: friendsResult.length ? parseInt(friendsResult[0].friend_count) : 0,
      };
    }
  }

  async getFollowerFriendsByUserId(getFollowerFriendsByUserIdParams: GetFollowerFriendsByUserIdParams): Promise<GetFollowerFriendsByUserIdReturn> {
    const { sessionId, userId, q, limit, page } = getFollowerFriendsByUserIdParams;

    const isFirstPerson = sessionId === userId;

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const result = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_followers ufo
      INNER JOIN user_friendships ufr ON ufr.friend_id = ufo.follower_id
      LEFT JOIN users u ON u.id = ufr.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code_code
      WHERE ufr.user_id = '${sessionId}'
      AND ufo.following_id = '${userId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      friends: result.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      friendCount: result.length,
    };
  }

  async followByUserId(followByUserIdParams: FollowByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = followByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't follow yourself", HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_followers',
        values: {
          follower_id: sessionId,
          following_id: userId,
        },
        onConflict: 'DO NOTHING',
      }),
    );

    return {};
  }

  async unfollowByUserId(unfollowByUserIdParams: UnfollowByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = unfollowByUserIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_followers')
      .where({
        follower_id: sessionId,
        following_id: userId,
      })
      .execute();

    return {};
  }

  async removeFollowerByUserId(removeFollowerByUserIdParams: RemoveFollowerByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId, followerId } = removeFollowerByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_followers')
      .where({
        follower_id: followerId,
        following_id: userId,
      })
      .execute();

    return {};
  }

  async subscribeByUserId(subscribeByUserIdParams: SubscribeByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = subscribeByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't subscribe to yourself", HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_subscribers',
        values: {
          subscriber_id: sessionId,
          user_id: userId,
        },
        onConflict: 'DO NOTHING',
      }),
    );

    return {};
  }

  async unsubscribeByUserId(unsubscribeByUserIdParams: UnsubscribeByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = unsubscribeByUserIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_subscribers')
      .where({
        subscriber_id: sessionId,
        user_id: userId,
      })
      .execute();

    return {};
  }

  async validateEmail(validateEmailParams: ValidateEmailParams): Promise<EmptyReturn> {
    const { ...rest } = validateEmailParams;
    return {};
  }

  async sendVerificationEmail(sendVerificationEmailParams: SendVerificationEmailParams): Promise<EmptyReturn> {
    const { ...rest } = sendVerificationEmailParams;
    return {};
  }

  async sendCreatorRequestForm(sendCreatorRequestFormParams: SendCreatorRequestFormParams): Promise<EmptyReturn> {
    const { ...rest } = sendCreatorRequestFormParams;
    return {};
  }

  async getUserBannerByUserId(getUserBannerByUserIdParams: GetUserBannerByUserIdParams): Promise<GetUserBannerByUserIdReturn> {
    const { sessionId, userId } = getUserBannerByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const userBannerResult = await this.dataSource.query(`
      SELECT id AS user_id, username, avatar_url, firstname, lastname
      FROM users
      WHERE id = '${userId}';
    `);

    return {
      userId: userBannerResult[0].user_id,
      username: userBannerResult[0].username,
      avatarUrl: userBannerResult[0].avatar_url,
      fullname: userBannerResult[0].firstname + ' ' + userBannerResult[0].lastname,
    };
  }
}
