import { IventListingTypeEnum } from 'src/constants/enums';
import { UserEduVerificationEnum } from 'src/entities';

export type DeleteUserByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type FollowByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type GetContactsByUserIdParams = {
  phoneNumbers: string[];
  sessionId: string;
  userId: string;
  limit: number;
  page: number;
};

export type GetFavoritesByUserIdParams = {
  sessionId: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetFollowerFriendsByUserIdParams = {
  sessionId: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetFollowersByUserIdParams = {
  sessionId: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetFollowingsByUserIdParams = {
  sessionId: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetIventsByUserIdParams = {
  sessionId: string;
  userId: string;
  type: IventListingTypeEnum;
  q: string;
  limit: number;
  page: number;
};

export type GetLevelByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type GetPagesByUserIdParams = {
  sessionId: string;
  userId: string;
  limit: number;
  page: number;
};

export type GetMemoryFoldersByUserIdParams = {
  sessionId: string;
  userId: string;
  limit: number;
  page: number;
};

export type GetUserByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type GetVibeFoldersByUserIdParams = {
  sessionId: string;
  userId: string;
  limit: number;
  page: number;
};

export type RegisterParams = {
  phoneNumber: string;
  fullname: string;
  hobbyIds: string[];
};

export type RemoveFollowerByUserIdParams = {
  sessionId: string;
  userId: string;
  followerId: string;
};

export type SendCreatorRequestFormParams = {
  sessionId: string;
  userId: string;
};

export type SendVerificationEmailParams = {
  sessionId: string;
  userId: string;
};

export type SubscribeByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type UnfollowByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type UnsubscribeByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type UpdateByUserIdParams = {
  sessionId: string;
  userId: string;
  newUsername: string;
  newBirthday: string;
  newGender: string;
  newAvatarUrl: string;
};

export type UpdateEmailByUserIdParams = {
  sessionId: string;
  userId: string;
  newEmail: string;
};

export type UpdateGradByUserIdParams = {
  sessionId: string;
  userId: string;
  newGrad: UserEduVerificationEnum;
};

export type UpdateNotificationsByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type UpdatePhoneNumberByUserIdParams = {
  sessionId: string;
  userId: string;
  newPhoneNumber: string;
};

export type UpdatePrivacyByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type ValidateEmailParams = {
  sessionId: string;
  userId: string;
};

export type GetUserBannerByUserIdParams = {
  sessionId: string;
  userId: string;
};
