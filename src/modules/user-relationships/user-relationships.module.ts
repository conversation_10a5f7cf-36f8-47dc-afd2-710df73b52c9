import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsModule } from '../notifications/notifications.module';
import { UserRelationshipsController } from './user-relationships.controller';
import { UserRelationshipsService } from './user-relationships.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserRelationship } from 'src/entities';

@Module({
  imports: [NotificationsModule, TypeOrmModule.forFeature([UserRelationship])],
  providers: [UserRelationshipsService],
  controllers: [UserRelationshipsController],
  exports: [],
})
export class UserRelationshipsModule {}
