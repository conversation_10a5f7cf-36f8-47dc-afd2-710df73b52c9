import { FriendListingTypeEnum } from 'src/constants/enums';

export type BlockUserByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type GetUserBlocklistParams = {
  sessionId: string;
};

export type InviteFriendByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type RemoveFriendByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type SearchFriendsByUserIdParams = {
  sessionId: string;
  userId: string;
  type: FriendListingTypeEnum;
  q: string;
  limit: number;
  page: number;
};

export type UnblockUserByUserIdParams = {
  sessionId: string;
  userId: string;
};

export type UninviteFriendByUserIdParams = {
  sessionId: string;
  userId: string;
};
