import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import {
  SearchBoxCategoryDto,
  SearchBoxCategoryListDto,
  SearchBoxForwardDto,
  SearchBoxRetrieveDto,
  SearchBoxReverseDto,
  SearchBoxSuggestDto,
} from './models/mapbox.dto';
import {
  SearchBoxCategoryListReturn,
  SearchBoxCategoryReturn,
  SearchBoxForwardReturn,
  SearchBoxRetrieveReturn,
  SearchBoxReverseReturn,
  SearchBoxSuggestReturn,
} from './models/mapbox.returns';

@Injectable()
export class MapboxService {
  private readonly baseUrl = 'https://api.mapbox.com/search';
  private readonly accessToken: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.accessToken = this.configService.get<string>('MAPBOX_ACCESS_TOKEN') || '';
    if (!this.accessToken) {
      throw new Error('MAPBOX_ACCESS_TOKEN environment variable is not defined');
    }
  }

  private buildQueryParams(params: Record<string, any>): Record<string, string> {
    const queryParams: Record<string, string> = {
      access_token: this.accessToken,
    };

    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        queryParams[key] = String(value);
      }
    });

    return queryParams;
  }

  async searchBoxSuggest(dto: SearchBoxSuggestDto): Promise<SearchBoxSuggestReturn> {
    try {
      const queryParams = this.buildQueryParams({
        q: dto.q,
        session_token: dto.sessionToken,
        language: dto.language,
        limit: dto.limit,
        proximity: dto.proximity,
        bbox: dto.bbox,
        country: dto.country,
        types: dto.types,
        poi_category: dto.poiCategory,
        poi_category_exclusions: dto.poiCategoryExclusions,
        eta_type: dto.etaType,
        navigation_profile: dto.navigationProfile,
        origin: dto.origin,
      });

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/searchbox/v1/suggest`, {
          params: queryParams,
        }),
      );

      return response.data;
    } catch (error: any) {
      throw new HttpException(
        `Mapbox Suggest API error: ${error.response?.data?.message || error.message}`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async searchBoxRetrieve(id: string, dto: SearchBoxRetrieveDto): Promise<SearchBoxRetrieveReturn> {
    try {
      const queryParams = this.buildQueryParams({
        session_token: dto.sessionToken,
        language: dto.language,
        eta_type: dto.etaType,
        navigation_profile: dto.navigationProfile,
        origin: dto.origin,
      });

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/searchbox/v1/retrieve/${id}`, {
          params: queryParams,
        }),
      );

      return response.data;
    } catch (error: any) {
      throw new HttpException(
        `Mapbox Retrieve API error: ${error.response?.data?.message || error.message}`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async searchBoxForward(dto: SearchBoxForwardDto): Promise<SearchBoxForwardReturn> {
    try {
      const queryParams = this.buildQueryParams({
        q: dto.q,
        language: dto.language,
        limit: dto.limit,
        proximity: dto.proximity,
        bbox: dto.bbox,
        country: dto.country,
        types: dto.types,
        poi_category: dto.poiCategory,
        poi_category_exclusions: dto.poiCategoryExclusions,
        auto_complete: dto.autoComplete,
        eta_type: dto.etaType,
        navigation_profile: dto.navigationProfile,
        origin: dto.origin,
      });

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/searchbox/v1/forward`, {
          params: queryParams,
        }),
      );

      return response.data;
    } catch (error: any) {
      throw new HttpException(
        `Mapbox Forward API error: ${error.response?.data?.message || error.message}`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async searchBoxCategory(canonicalCategoryId: string, dto: SearchBoxCategoryDto): Promise<SearchBoxCategoryReturn> {
    try {
      const queryParams = this.buildQueryParams({
        language: dto.language,
        proximity: dto.proximity,
        bbox: dto.bbox,
        country: dto.country,
        types: dto.types,
        poi_category_exclusions: dto.poiCategoryExclusions,
        sar_type: dto.sarType,
        route: dto.route,
        route_geometry: dto.routeGeometry,
        time_deviation: dto.timeDeviation,
      });

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/searchbox/v1/category/${canonicalCategoryId}`, {
          params: queryParams,
        }),
      );

      return response.data;
    } catch (error: any) {
      throw new HttpException(
        `Mapbox Category API error: ${error.response?.data?.message || error.message}`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async searchBoxCategoryList(dto: SearchBoxCategoryListDto): Promise<SearchBoxCategoryListReturn> {
    try {
      const queryParams = this.buildQueryParams({
        language: dto.language,
      });

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/searchbox/v1/list/category`, {
          params: queryParams,
        }),
      );

      return response.data;
    } catch (error: any) {
      throw new HttpException(
        `Mapbox Category List API error: ${error.response?.data?.message || error.message}`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async searchBoxReverse(dto: SearchBoxReverseDto): Promise<SearchBoxReverseReturn> {
    try {
      const queryParams = this.buildQueryParams({
        longitude: dto.longitude,
        latitude: dto.latitude,
        language: dto.language,
        limit: dto.limit,
        country: dto.country,
        types: dto.types,
      });

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/searchbox/v1/reverse`, {
          params: queryParams,
        }),
      );

      return response.data;
    } catch (error: any) {
      throw new HttpException(
        `Mapbox Reverse API error: ${error.response?.data?.message || error.message}`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
