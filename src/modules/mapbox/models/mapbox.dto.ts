import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

export class SearchBoxSuggestDto {
  @ApiProperty({
    description: 'Search query string',
    example: 'coffee shop',
    minLength: 1,
    maxLength: 256,
  })
  @IsString()
  q!: string;

  @ApiProperty({
    description: 'Session token for grouping related requests',
    example: 'session_123456789',
    minLength: 1,
    maxLength: 256,
  })
  @IsString()
  sessionToken!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Language code for results (ISO 639-1)',
    example: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string | null;

  @ApiProperty({
    type: 'integer',
    nullable: true,
    required: false,
    description: 'Maximum number of results to return',
    example: 10,
    minimum: 1,
    maximum: 10,
  })
  @IsNumber()
  @Min(1)
  @Max(10)
  @IsOptional()
  limit?: number | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Proximity bias as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @IsString()
  @IsOptional()
  proximity?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Bounding box as minX,minY,maxX,maxY',
    example: '28.9,40.9,29.1,41.1',
  })
  @IsString()
  @IsOptional()
  bbox?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @IsString()
  @IsOptional()
  country?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Feature types to include',
    example: 'poi',
  })
  @IsString()
  @IsOptional()
  types?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'POI category filter',
    example: 'food_and_drink',
  })
  @IsString()
  @IsOptional()
  poiCategory?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'POI categories to exclude',
    example: 'gas_station',
  })
  @IsString()
  @IsOptional()
  poiCategoryExclusions?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'ETA calculation type',
    example: 'navigation',
  })
  @IsString()
  @IsOptional()
  etaType?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Navigation profile for ETA calculation',
    example: 'driving',
  })
  @IsString()
  @IsOptional()
  navigationProfile?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Origin point for ETA calculation as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @IsString()
  @IsOptional()
  origin?: string | null;
}

export class SearchBoxRetrieveDto {
  @ApiProperty({
    description: 'Mapbox ID of the feature to retrieve',
    example: 'dXJuOm1ieHBvaTo0ZDk1YjJkNy05YzQyLTQ4YjMtOGI4Zi1lNzJlNzc2YzI4YzI',
  })
  @IsString()
  id!: string;

  @ApiProperty({
    description: 'Session token for grouping related requests',
    example: 'session_123456789',
    minLength: 1,
    maxLength: 256,
  })
  @IsString()
  sessionToken!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Language code for results (ISO 639-1)',
    example: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'ETA calculation type',
    example: 'navigation',
  })
  @IsString()
  @IsOptional()
  etaType?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Navigation profile for ETA calculation',
    example: 'driving',
  })
  @IsString()
  @IsOptional()
  navigationProfile?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Origin point for ETA calculation as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @IsString()
  @IsOptional()
  origin?: string | null;
}

export class SearchBoxForwardDto {
  @ApiProperty({
    description: 'Search query string',
    example: 'coffee shop',
    minLength: 1,
    maxLength: 256,
  })
  @IsString()
  q!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Language code for results (ISO 639-1)',
    example: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string | null;

  @ApiProperty({
    type: 'integer',
    nullable: true,
    required: false,
    description: 'Maximum number of results to return',
    example: 10,
    minimum: 1,
    maximum: 10,
  })
  @IsNumber()
  @Min(1)
  @Max(10)
  @IsOptional()
  limit?: number | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Proximity bias as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @IsString()
  @IsOptional()
  proximity?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Bounding box as minX,minY,maxX,maxY',
    example: '28.9,40.9,29.1,41.1',
  })
  @IsString()
  @IsOptional()
  bbox?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @IsString()
  @IsOptional()
  country?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Feature types to include',
    example: 'poi',
  })
  @IsString()
  @IsOptional()
  types?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'POI category filter',
    example: 'food_and_drink',
  })
  @IsString()
  @IsOptional()
  poiCategory?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'POI categories to exclude',
    example: 'gas_station',
  })
  @IsString()
  @IsOptional()
  poiCategoryExclusions?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Auto-complete setting',
    example: 'true',
  })
  @IsString()
  @IsOptional()
  autoComplete?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'ETA calculation type',
    example: 'navigation',
  })
  @IsString()
  @IsOptional()
  etaType?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Navigation profile for ETA calculation',
    example: 'driving',
  })
  @IsString()
  @IsOptional()
  navigationProfile?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Origin point for ETA calculation as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @IsString()
  @IsOptional()
  origin?: string | null;
}

export class SearchBoxCategoryDto {
  @ApiProperty({
    description: 'Canonical category ID',
    example: 'food_and_drink',
  })
  @IsString()
  canonicalCategoryId!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Language code for results (ISO 639-1)',
    example: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Proximity bias as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @IsString()
  @IsOptional()
  proximity?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Bounding box as minX,minY,maxX,maxY',
    example: '28.9,40.9,29.1,41.1',
  })
  @IsString()
  @IsOptional()
  bbox?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @IsString()
  @IsOptional()
  country?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Feature types to include',
    example: 'poi',
  })
  @IsString()
  @IsOptional()
  types?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'POI categories to exclude',
    example: 'gas_station',
  })
  @IsString()
  @IsOptional()
  poiCategoryExclusions?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Search along route type',
    example: 'isochrone',
  })
  @IsString()
  @IsOptional()
  sarType?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Route geometry for search along route',
    example: 'encoded_polyline_string',
  })
  @IsString()
  @IsOptional()
  route?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Route geometry format',
    example: 'polyline',
  })
  @IsString()
  @IsOptional()
  routeGeometry?: string | null;

  @ApiProperty({
    type: 'integer',
    nullable: true,
    required: false,
    description: 'Time deviation in minutes',
    example: 15,
    minimum: 1,
    maximum: 60,
  })
  @IsNumber()
  @Min(1)
  @Max(60)
  @IsOptional()
  timeDeviation?: number | null;
}

export class SearchBoxCategoryListDto {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Language code for results (ISO 639-1)',
    example: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string | null;
}

export class SearchBoxReverseDto {
  @ApiProperty({
    type: 'double',
    description: 'Longitude coordinate',
    example: 28.9784,
    minimum: -180,
    maximum: 180,
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude!: number;

  @ApiProperty({
    type: 'double',
    description: 'Latitude coordinate',
    example: 41.0082,
    minimum: -90,
    maximum: 90,
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Language code for results (ISO 639-1)',
    example: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string | null;

  @ApiProperty({
    type: 'integer',
    nullable: true,
    required: false,
    description: 'Maximum number of results to return',
    example: 10,
    minimum: 1,
    maximum: 10,
  })
  @IsNumber()
  @Min(1)
  @Max(10)
  @IsOptional()
  limit?: number | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @IsString()
  @IsOptional()
  country?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Feature types to include',
    example: 'poi',
  })
  @IsString()
  @IsOptional()
  types?: string | null;
}
