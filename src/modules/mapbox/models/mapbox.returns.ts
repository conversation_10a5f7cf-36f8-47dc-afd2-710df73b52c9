import { ApiProperty } from '@nestjs/swagger';

// Common nested classes used across multiple return types
export class SearchBoxGeometry {
  @ApiProperty({
    description: 'Geometry type',
    example: 'Point',
  })
  type!: string;

  @ApiProperty({
    type: 'double',
    isArray: true,
    description: 'Coordinates array [longitude, latitude]',
    example: [28.9784, 41.0082],
  })
  coordinates!: number[];
}

export class SearchBoxRoutablePoints {
  @ApiProperty({
    description: 'Name of the routable point',
    example: 'main_entrance',
  })
  name!: string;

  @ApiProperty({
    type: 'double',
    description: 'Latitude coordinate',
    example: 41.0082,
  })
  latitude!: number;

  @ApiProperty({
    type: 'double',
    description: 'Longitude coordinate',
    example: 28.9784,
  })
  longitude!: number;
}

export class SearchBoxCoordinates {
  @ApiProperty({
    type: 'double',
    description: 'Longitude coordinate',
    example: 28.9784,
  })
  longitude!: number;

  @ApiProperty({
    type: 'double',
    description: 'Latitude coordinate',
    example: 41.0082,
  })
  latitude!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Accuracy of the coordinates',
    example: 'rooftop',
  })
  accuracy?: string | null;

  @ApiProperty({
    type: [SearchBoxRoutablePoints],
    nullable: true,
    required: false,
    description: 'Routable points for navigation',
    example: [],
  })
  routable_points!: SearchBoxRoutablePoints[] | null;
}

// Context classes
export class SearchBoxContextRegion {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Region ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Region name',
    example: 'Istanbul',
  })
  name?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Region code',
    example: '34',
  })
  region_code?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Full region code',
    example: 'TR-34',
  })
  region_code_full?: string | null;
}

export class SearchBoxContextPostcode {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Postcode ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Postcode name',
    example: '34000',
  })
  name?: string | null;
}

export class SearchBoxContextDistrict {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'District ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'District name',
    example: 'Beyoğlu',
  })
  name?: string | null;
}

export class SearchBoxContextPlace {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Place ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Place name',
    example: 'Istanbul',
  })
  name?: string | null;
}

export class SearchBoxContextLocality {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Locality ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Locality name',
    example: 'Galata',
  })
  name?: string | null;
}

export class SearchBoxContextNeighborhood {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Neighborhood ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Neighborhood name',
    example: 'Karaköy',
  })
  name?: string | null;
}

export class SearchBoxContextAddress {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Address ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Address name',
    example: '123 Main Street',
  })
  name?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Address number',
    example: '123',
  })
  address_number?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Street name',
    example: 'Main Street',
  })
  street_name?: string | null;
}

export class SearchBoxContextStreet {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Street ID',
    example: 'dXJuOm1ieHBsYzpBZ0lC',
  })
  id?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Street name',
    example: 'Main Street',
  })
  name?: string | null;
}

export class SearchBoxContext {
  @ApiProperty({
    type: SearchBoxContextRegion,
    nullable: true,
    required: false,
    description: 'Region context information',
  })
  region?: SearchBoxContextRegion | null;

  @ApiProperty({
    type: SearchBoxContextPostcode,
    nullable: true,
    required: false,
    description: 'Postcode context information',
  })
  postcode?: SearchBoxContextPostcode | null;

  @ApiProperty({
    type: SearchBoxContextDistrict,
    nullable: true,
    required: false,
    description: 'District context information',
  })
  district?: SearchBoxContextDistrict | null;

  @ApiProperty({
    type: SearchBoxContextPlace,
    nullable: true,
    required: false,
    description: 'Place context information',
  })
  place?: SearchBoxContextPlace | null;

  @ApiProperty({
    type: SearchBoxContextLocality,
    nullable: true,
    required: false,
    description: 'Locality context information',
  })
  locality?: SearchBoxContextLocality | null;

  @ApiProperty({
    type: SearchBoxContextNeighborhood,
    nullable: true,
    required: false,
    description: 'Neighborhood context information',
  })
  neighborhood?: SearchBoxContextNeighborhood | null;

  @ApiProperty({
    type: SearchBoxContextAddress,
    nullable: true,
    required: false,
    description: 'Address context information',
  })
  address?: SearchBoxContextAddress | null;

  @ApiProperty({
    type: SearchBoxContextStreet,
    nullable: true,
    required: false,
    description: 'Street context information',
  })
  street?: SearchBoxContextStreet | null;
}

export class SearchBoxBaseProperties {
  @ApiProperty({
    description: 'Name of the place',
    example: 'Starbucks',
  })
  name!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Preferred name of the place',
    example: 'Starbucks Coffee',
  })
  name_preferred?: string | null;

  @ApiProperty({
    description: 'Mapbox ID of the place',
    example: 'dXJuOm1ieHBvaTo0ZDk1YjJkNy05YzQyLTQ4YjMtOGI4Zi1lNzJlNzc2YzI4YzI',
  })
  mapbox_id!: string;

  @ApiProperty({
    description: 'Feature type',
    example: 'poi',
  })
  feature_type!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Address of the place',
    example: '123 Main Street',
  })
  address?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Full address of the place',
    example: '123 Main Street, Beyoğlu, Istanbul, Turkey',
  })
  full_address?: string | null;

  @ApiProperty({
    description: 'Formatted place description',
    example: 'Starbucks, Beyoğlu, Istanbul',
  })
  place_formatted!: string;

  @ApiProperty({
    type: SearchBoxContext,
    description: 'Context information about the place',
  })
  context!: SearchBoxContext;

  @ApiProperty({
    description: 'Language of the result',
    example: 'en',
  })
  language!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Maki icon identifier',
    example: 'cafe',
  })
  maki?: string | null;

  @ApiProperty({
    type: [String],
    nullable: true,
    required: false,
    description: 'POI categories',
    example: ['food_and_drink', 'cafe'],
  })
  poi_category?: string[] | null;

  @ApiProperty({
    type: [String],
    nullable: true,
    required: false,
    description: 'POI category IDs',
    example: ['food_and_drink_cafe'],
  })
  poi_category_ids?: string[] | null;

  @ApiProperty({
    type: [String],
    nullable: true,
    required: false,
    description: 'Brand names',
    example: ['Starbucks'],
  })
  brand?: string[] | null;

  @ApiProperty({
    type: [String],
    nullable: true,
    required: false,
    description: 'Brand IDs',
    example: ['starbucks-c8b9b4'],
  })
  brand_id?: string[] | null;

  @ApiProperty({
    type: 'object',
    nullable: true,
    required: false,
    description: 'External IDs mapping',
    example: { foursquare: '4b123456f964a520123456e3' },
  })
  external_ids?: Record<string, string> | null;

  @ApiProperty({
    type: 'object',
    nullable: true,
    required: false,
    description: 'Additional metadata',
    example: { phone: '+1234567890', website: 'https://starbucks.com' },
  })
  metadata?: Record<string, any> | null;
}

export class SearchBoxProperties extends SearchBoxBaseProperties {
  @ApiProperty({
    type: SearchBoxCoordinates,
    description: 'Coordinates of the place',
  })
  coordinates!: SearchBoxCoordinates;

  @ApiProperty({
    type: 'double',
    isArray: true,
    nullable: true,
    required: false,
    description: 'Bounding box coordinates [minX, minY, maxX, maxY]',
    example: [28.978, 41.008, 28.9788, 41.0084],
  })
  bbox?: number[] | null;
}

// Feature classes for different return types
export class SearchBoxSuggestFeature extends SearchBoxBaseProperties {
  @ApiProperty({
    type: 'integer',
    nullable: true,
    required: false,
    description: 'Distance in meters',
    example: 150,
    minimum: 0,
  })
  distance?: number | null;

  @ApiProperty({
    type: 'double',
    nullable: true,
    required: false,
    description: 'Estimated time of arrival in minutes',
    example: 5.2,
    minimum: 0,
  })
  eta?: number | null;

  @ApiProperty({
    type: 'double',
    nullable: true,
    required: false,
    description: 'Added distance in meters',
    example: 200.5,
    minimum: 0,
  })
  added_distance?: number | null;

  @ApiProperty({
    type: 'double',
    nullable: true,
    required: false,
    description: 'Added time in minutes',
    example: 3.1,
    minimum: 0,
  })
  added_time?: number | null;
}

export class SearchBoxFeature {
  @ApiProperty({
    description: 'Feature type',
    example: 'Feature',
  })
  type!: string;

  @ApiProperty({
    type: SearchBoxGeometry,
    description: 'Geometry of the feature',
  })
  geometry!: SearchBoxGeometry;

  @ApiProperty({
    type: SearchBoxProperties,
    description: 'Properties of the feature',
  })
  properties!: SearchBoxProperties;
}

// Main return types for API endpoints
export class SearchBoxSuggestReturn {
  @ApiProperty({
    type: [SearchBoxSuggestFeature],
    description: 'List of suggested places',
    example: [],
  })
  suggestions!: SearchBoxSuggestFeature[];

  @ApiProperty({
    description: 'Attribution text',
    example: '© 2024 Mapbox, © OpenStreetMap',
  })
  attribution!: string;
}

export class SearchBoxRetrieveReturn {
  @ApiProperty({
    type: [SearchBoxFeature],
    description: 'List of retrieved features',
    example: [],
  })
  features!: SearchBoxFeature[];

  @ApiProperty({
    description: 'Attribution text',
    example: '© 2024 Mapbox, © OpenStreetMap',
  })
  attribution!: string;
}

export class SearchBoxForwardReturn {
  @ApiProperty({
    description: 'Collection type',
    example: 'FeatureCollection',
  })
  type!: string;

  @ApiProperty({
    type: [SearchBoxFeature],
    description: 'List of features found',
    example: [],
  })
  features!: SearchBoxFeature[];

  @ApiProperty({
    description: 'Attribution text',
    example: '© 2024 Mapbox, © OpenStreetMap',
  })
  attribution!: string;
}

export class SearchBoxCategoryReturn {
  @ApiProperty({
    description: 'Collection type',
    example: 'FeatureCollection',
  })
  type!: string;

  @ApiProperty({
    type: [SearchBoxFeature],
    description: 'List of features in the category',
    example: [],
  })
  features!: SearchBoxFeature[];

  @ApiProperty({
    description: 'Attribution text',
    example: '© 2024 Mapbox, © OpenStreetMap',
  })
  attribution!: string;
}

export class SearchBoxCategoryItem {
  @ApiProperty({
    description: 'Canonical category ID',
    example: 'food_and_drink',
  })
  canonical_id!: string;

  @ApiProperty({
    description: 'Maki icon identifier',
    example: 'cafe',
  })
  icon!: string;

  @ApiProperty({
    description: 'Category name',
    example: 'Food and Drink',
  })
  name!: string;
}

export class SearchBoxCategoryListReturn {
  @ApiProperty({
    type: [SearchBoxCategoryItem],
    description: 'List of available categories',
    example: [],
  })
  list_items!: SearchBoxCategoryItem[];

  @ApiProperty({
    description: 'Attribution text',
    example: '© 2024 Mapbox, © OpenStreetMap',
  })
  attribution!: string;

  @ApiProperty({
    description: 'Version of the API',
    example: '1.0.0',
  })
  version!: string;
}

export class SearchBoxReverseReturn {
  @ApiProperty({
    description: 'Collection type',
    example: 'FeatureCollection',
  })
  type!: string;

  @ApiProperty({
    type: [SearchBoxFeature],
    description: 'List of features found at the location',
    example: [],
  })
  features!: SearchBoxFeature[];

  @ApiProperty({
    description: 'Attribution text',
    example: '© 2024 Mapbox, © OpenStreetMap',
  })
  attribution!: string;
}
