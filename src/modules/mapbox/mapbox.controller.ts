import { <PERSON>, DefaultValuePipe, Get, Param, ParseFloatPipe, Query } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MapboxService } from './mapbox.service';
import {
  SearchBoxCategoryListReturn,
  SearchBoxCategoryReturn,
  SearchBoxForwardReturn,
  SearchBoxRetrieveReturn,
  SearchBoxReverseReturn,
  SearchBoxSuggestReturn,
} from './models/mapbox.returns';

@ApiTags('mapbox')
@Controller('mapbox')
export class MapboxController {
  constructor(private readonly mapboxService: MapboxService) {}

  @ApiOperation({
    summary: 'Search for place suggestions',
    description: 'Returns a list of place suggestions based on the search query',
  })
  @ApiResponse({
    status: 200,
    type: SearchBoxSuggestReturn,
    description: 'List of place suggestions',
  })
  @ApiQuery({ name: 'q', description: 'Search query string', example: 'coffee shop' })
  @ApiQuery({ name: 'sessionToken', description: 'Session token for grouping requests', example: 'session_123' })
  @ApiQuery({ name: 'language', required: false, description: 'Language code (ISO 639-1)', example: 'en' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer', description: 'Maximum number of results', example: 10 })
  @ApiQuery({
    name: 'proximity',
    required: false,
    description: 'Proximity bias as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @ApiQuery({
    name: 'bbox',
    required: false,
    description: 'Bounding box as minX,minY,maxX,maxY',
    example: '28.9,40.9,29.1,41.1',
  })
  @ApiQuery({
    name: 'country',
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @ApiQuery({ name: 'types', required: false, description: 'Feature types to include', example: 'poi' })
  @ApiQuery({ name: 'poiCategory', required: false, description: 'POI category filter', example: 'food_and_drink' })
  @ApiQuery({
    name: 'poiCategoryExclusions',
    required: false,
    description: 'POI categories to exclude',
    example: 'gas_station',
  })
  @ApiQuery({ name: 'etaType', required: false, description: 'ETA calculation type', example: 'navigation' })
  @ApiQuery({
    name: 'navigationProfile',
    required: false,
    description: 'Navigation profile for ETA',
    example: 'driving',
  })
  @ApiQuery({
    name: 'origin',
    required: false,
    description: 'Origin point for ETA as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @Get('suggest')
  async searchBoxSuggest(
    @Query('q') q: string,
    @Query('sessionToken') sessionToken: string,
    @Query('language') language?: string,
    @Query('limit', new DefaultValuePipe(null)) limit?: number,
    @Query('proximity') proximity?: string,
    @Query('bbox') bbox?: string,
    @Query('country') country?: string,
    @Query('types') types?: string,
    @Query('poiCategory') poiCategory?: string,
    @Query('poiCategoryExclusions') poiCategoryExclusions?: string,
    @Query('etaType') etaType?: string,
    @Query('navigationProfile') navigationProfile?: string,
    @Query('origin') origin?: string,
  ) {
    const result = await this.mapboxService.searchBoxSuggest({
      q,
      sessionToken,
      language: language || null,
      limit: limit || null,
      proximity: proximity || null,
      bbox: bbox || null,
      country: country || null,
      types: types || null,
      poiCategory: poiCategory || null,
      poiCategoryExclusions: poiCategoryExclusions || null,
      etaType: etaType || null,
      navigationProfile: navigationProfile || null,
      origin: origin || null,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Retrieve detailed information about a place',
    description: 'Returns detailed information about a specific place using its Mapbox ID',
  })
  @ApiResponse({
    status: 200,
    type: SearchBoxRetrieveReturn,
    description: 'Detailed place information',
  })
  @ApiQuery({ name: 'sessionToken', description: 'Session token for grouping requests', example: 'session_123' })
  @ApiQuery({ name: 'language', required: false, description: 'Language code (ISO 639-1)', example: 'en' })
  @ApiQuery({ name: 'etaType', required: false, description: 'ETA calculation type', example: 'navigation' })
  @ApiQuery({
    name: 'navigationProfile',
    required: false,
    description: 'Navigation profile for ETA',
    example: 'driving',
  })
  @ApiQuery({
    name: 'origin',
    required: false,
    description: 'Origin point for ETA as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @Get('retrieve/:id')
  async searchBoxRetrieve(
    @Param('id') id: string,
    @Query('sessionToken') sessionToken: string,
    @Query('language') language?: string,
    @Query('etaType') etaType?: string,
    @Query('navigationProfile') navigationProfile?: string,
    @Query('origin') origin?: string,
  ) {
    const result = await this.mapboxService.searchBoxRetrieve(id, {
      id,
      sessionToken,
      language: language || null,
      etaType: etaType || null,
      navigationProfile: navigationProfile || null,
      origin: origin || null,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Forward geocoding search',
    description: 'Search for places using forward geocoding',
  })
  @ApiResponse({
    status: 200,
    type: SearchBoxForwardReturn,
    description: 'Forward geocoding results',
  })
  @ApiQuery({ name: 'q', description: 'Search query string', example: 'coffee shop' })
  @ApiQuery({ name: 'language', required: false, description: 'Language code (ISO 639-1)', example: 'en' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer', description: 'Maximum number of results', example: 10 })
  @ApiQuery({
    name: 'proximity',
    required: false,
    description: 'Proximity bias as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @ApiQuery({
    name: 'bbox',
    required: false,
    description: 'Bounding box as minX,minY,maxX,maxY',
    example: '28.9,40.9,29.1,41.1',
  })
  @ApiQuery({
    name: 'country',
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @ApiQuery({ name: 'types', required: false, description: 'Feature types to include', example: 'poi' })
  @ApiQuery({ name: 'poiCategory', required: false, description: 'POI category filter', example: 'food_and_drink' })
  @ApiQuery({
    name: 'poiCategoryExclusions',
    required: false,
    description: 'POI categories to exclude',
    example: 'gas_station',
  })
  @ApiQuery({ name: 'autoComplete', required: false, description: 'Auto-complete setting', example: 'true' })
  @ApiQuery({ name: 'etaType', required: false, description: 'ETA calculation type', example: 'navigation' })
  @ApiQuery({
    name: 'navigationProfile',
    required: false,
    description: 'Navigation profile for ETA',
    example: 'driving',
  })
  @ApiQuery({
    name: 'origin',
    required: false,
    description: 'Origin point for ETA as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @Get('forward')
  async searchBoxForward(
    @Query('q') q: string,
    @Query('language') language?: string,
    @Query('limit', new DefaultValuePipe(null)) limit?: number,
    @Query('proximity') proximity?: string,
    @Query('bbox') bbox?: string,
    @Query('country') country?: string,
    @Query('types') types?: string,
    @Query('poiCategory') poiCategory?: string,
    @Query('poiCategoryExclusions') poiCategoryExclusions?: string,
    @Query('autoComplete') autoComplete?: string,
    @Query('etaType') etaType?: string,
    @Query('navigationProfile') navigationProfile?: string,
    @Query('origin') origin?: string,
  ) {
    const result = await this.mapboxService.searchBoxForward({
      q,
      language: language || null,
      limit: limit || null,
      proximity: proximity || null,
      bbox: bbox || null,
      country: country || null,
      types: types || null,
      poiCategory: poiCategory || null,
      poiCategoryExclusions: poiCategoryExclusions || null,
      autoComplete: autoComplete || null,
      etaType: etaType || null,
      navigationProfile: navigationProfile || null,
      origin: origin || null,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Search places by category',
    description: 'Search for places within a specific category',
  })
  @ApiResponse({
    status: 200,
    type: SearchBoxCategoryReturn,
    description: 'Category search results',
  })
  @ApiQuery({ name: 'language', required: false, description: 'Language code (ISO 639-1)', example: 'en' })
  @ApiQuery({
    name: 'proximity',
    required: false,
    description: 'Proximity bias as longitude,latitude',
    example: '28.9784,41.0082',
  })
  @ApiQuery({
    name: 'bbox',
    required: false,
    description: 'Bounding box as minX,minY,maxX,maxY',
    example: '28.9,40.9,29.1,41.1',
  })
  @ApiQuery({
    name: 'country',
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @ApiQuery({ name: 'types', required: false, description: 'Feature types to include', example: 'poi' })
  @ApiQuery({
    name: 'poiCategoryExclusions',
    required: false,
    description: 'POI categories to exclude',
    example: 'gas_station',
  })
  @ApiQuery({ name: 'sarType', required: false, description: 'Search along route type', example: 'isochrone' })
  @ApiQuery({ name: 'route', required: false, description: 'Route geometry for search along route' })
  @ApiQuery({ name: 'routeGeometry', required: false, description: 'Route geometry format', example: 'polyline' })
  @ApiQuery({
    name: 'timeDeviation',
    required: false,
    type: 'integer',
    description: 'Time deviation in minutes',
    example: 15,
  })
  @Get('category/:canonicalCategoryId')
  async searchBoxCategory(
    @Param('canonicalCategoryId') canonicalCategoryId: string,
    @Query('language') language?: string,
    @Query('proximity') proximity?: string,
    @Query('bbox') bbox?: string,
    @Query('country') country?: string,
    @Query('types') types?: string,
    @Query('poiCategoryExclusions') poiCategoryExclusions?: string,
    @Query('sarType') sarType?: string,
    @Query('route') route?: string,
    @Query('routeGeometry') routeGeometry?: string,
    @Query('timeDeviation', new DefaultValuePipe(null)) timeDeviation?: number,
  ) {
    const result = await this.mapboxService.searchBoxCategory(canonicalCategoryId, {
      canonicalCategoryId,
      language: language || null,
      proximity: proximity || null,
      bbox: bbox || null,
      country: country || null,
      types: types || null,
      poiCategoryExclusions: poiCategoryExclusions || null,
      sarType: sarType || null,
      route: route || null,
      routeGeometry: routeGeometry || null,
      timeDeviation: timeDeviation || null,
    });
    return result;
  }

  @ApiOperation({
    summary: 'List available categories',
    description: 'Returns a list of all available POI categories',
  })
  @ApiResponse({
    status: 200,
    type: SearchBoxCategoryListReturn,
    description: 'List of available categories',
  })
  @ApiQuery({ name: 'language', required: false, description: 'Language code (ISO 639-1)', example: 'en' })
  @Get('categories')
  async searchBoxCategoryList(@Query('language') language?: string) {
    const result = await this.mapboxService.searchBoxCategoryList({
      language: language || null,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Reverse geocoding search',
    description: 'Find places near a specific coordinate',
  })
  @ApiResponse({
    status: 200,
    type: SearchBoxReverseReturn,
    description: 'Reverse geocoding results',
  })
  @ApiQuery({ name: 'longitude', type: 'double', description: 'Longitude coordinate', example: 28.9784 })
  @ApiQuery({ name: 'latitude', type: 'double', description: 'Latitude coordinate', example: 41.0082 })
  @ApiQuery({ name: 'language', required: false, description: 'Language code (ISO 639-1)', example: 'en' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer', description: 'Maximum number of results', example: 10 })
  @ApiQuery({
    name: 'country',
    required: false,
    description: 'Country code filter (ISO 3166-1 alpha-2)',
    example: 'TR',
  })
  @ApiQuery({ name: 'types', required: false, description: 'Feature types to include', example: 'poi' })
  @Get('reverse')
  async searchBoxReverse(
    @Query('longitude', ParseFloatPipe) longitude: number,
    @Query('latitude', ParseFloatPipe) latitude: number,
    @Query('language') language?: string,
    @Query('limit', new DefaultValuePipe(null)) limit?: number,
    @Query('country') country?: string,
    @Query('types') types?: string,
  ) {
    const result = await this.mapboxService.searchBoxReverse({
      longitude,
      latitude,
      language: language || null,
      limit: limit || null,
      country: country || null,
      types: types || null,
    });
    return result;
  }
}
