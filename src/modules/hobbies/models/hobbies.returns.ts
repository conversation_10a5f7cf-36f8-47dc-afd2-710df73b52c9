import { ApiProperty } from '@nestjs/swagger';
import { HobbyItem } from 'src/models';

export class SearchHobbiesReturn {
  @ApiProperty({
    type: [HobbyItem],
    description: 'List of hobby categories',
    example: [],
  })
  hobbies!: HobbyItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of hobby categories',
    example: 2,
    minimum: 0,
  })
  hobbyCount!: number;
}
