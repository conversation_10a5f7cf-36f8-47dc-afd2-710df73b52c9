import { MemoryOriginEnum } from 'src/constants/enums/memory-origin-enum';
import { MediaFormatEnum } from 'src/entities/enums/shared/media-format-enum';

export type CreateMemoryParams = {
  sessionId: string;
  mediaFormat: MediaFormatEnum;
  caption?: string | null;
  squadId: string;
};

export type DeleteMemoryByMemoryIdParams = {
  sessionId: string;
  memoryId: string;
};

export type GetMemoryByMemoryIdParams = {
  sessionId: string;
  memoryId: string;
  origin: MemoryOriginEnum;
};
