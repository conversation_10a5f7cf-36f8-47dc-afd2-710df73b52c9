import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsUUID, Matches } from 'class-validator';

export class CreateGroupDto {
  @ApiProperty({
    description: 'Group name can only contain letters, numbers, underscores, hyphens, and periods',
    example: 'My Awesome Group',
    minLength: 3,
    maxLength: 50,
  })
  @Matches(/^[\p{L}0-9 _\-\.]{3,50}$/u, {
    message: 'Group name can only contain letters, numbers, spaces, underscores, hyphens, and dots (3-50 characters)',
  })
  groupName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Base64 encoded thumbnail image buffer for the group',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...',
  })
  @IsString()
  @IsOptional()
  thumbnailBuffer?: string | null;

  @ApiProperty({
    format: 'uuid',
    description: 'Array of user UUIDs to add as group members',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each user ID must be a valid UUID v4' })
  userIds!: string[];
}
