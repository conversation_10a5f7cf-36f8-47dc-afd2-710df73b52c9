export type SearchModeratorsForPageCreationParams = {
  sessionId: string;
  sessionPageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchModeratorsToAddByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type AddPageMembersByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  userIds: string[];
};

export type JoinPageMembershipByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type LeavePageMembershipByPageIdParams = {
  sessionId: string;
  pageId: string;
};

export type LeavePageModerationByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
};

export type RemovePageMemberByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  userId: string;
};

export type RemovePageModeratorByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  userId: string;
};

export type SearchPageAdminsByPageIdParams = {
  sessionId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchPageMembersByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchUsersToAddByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchAdministrationByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type TransferPageAdministrationByPageIdParams = {
  sessionId: string;
  sessionPageId: string;
  pageId: string;
  userId: string;
};
