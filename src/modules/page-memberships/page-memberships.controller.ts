import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import {
  AddPageMembersByPageIdDto,
  RemovePageMemberByPageIdDto,
  RemovePageModeratorByPageIdDto,
  TransferPageAdministrationByPageIdDto,
} from './models/page-memberships.dto';
import {
  SearchAdministrationByPageIdReturn,
  SearchModeratorsForPageCreationReturn,
  SearchPageMembersByPageIdReturn,
  SearchUsersToAddByPageIdReturn,
} from './models/page-memberships.returns';
import { PageMembershipsService } from './page-memberships.service';

@ApiTags('pageMemberships')
@Controller('pageMemberships')
export class PageMembershipsController {
  constructor(private readonly pageMembershipsService: PageMembershipsService) {}

  @ApiOperation({
    summary: 'Sayfa oluştururken uygun yardımcı adminler listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchModeratorsForPageCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('administration/search')
  async searchModeratorsForPageCreation(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchModeratorsForPageCreation({
      sessionId,

      sessionPageId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Var olan sayfaya eklenebilecek uygun yardımcı adminler listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchModeratorsForPageCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/administration/search')
  async searchModeratorsToAddByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchModeratorsToAddByPageId({
      sessionId,

      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile adminlikten ayrılınır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/administration/leave')
  async leavePageModerationByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.leavePageModerationByPageId({
      sessionId,

      sessionPageId,
      pageId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile yardımcı admin çıkartılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/administration/remove')
  async removePageModeratorByPageId(
    @Body()
    removePageModeratorByPageIdDto: RemovePageModeratorByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.removePageModeratorByPageId({
      sessionId,

      sessionPageId,
      pageId,
      ...removePageModeratorByPageIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile adminlik devredilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/administration/transfer')
  async transferPageAdministrationByPageId(
    @Body()
    transferPageAdministrationByPageIdDto: TransferPageAdministrationByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.transferPageAdministrationByPageId({
      sessionId,

      sessionPageId,
      pageId,
      ...transferPageAdministrationByPageIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üyeler listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchPageMembersByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/members')
  async searchPageMembersByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchPageMembersByPageId({
      sessionId,

      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile eklenebilecek hesaplar listelenir',
  })
  @ApiResponse({
    status: 200,
    type: SearchUsersToAddByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/search')
  async searchUsersToAddByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchUsersToAddByPageId({
      sessionId,

      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile yetkilileri listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchAdministrationByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':pageId/administration')
  async searchAdministrationByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.searchAdministrationByPageId({
      sessionId,

      sessionPageId,
      pageId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üye olunur',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/join')
  async joinPageMembershipByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.pageMembershipsService.joinPageMembershipByPageId({
      sessionId,

      pageId,
    });
    return result;
  }
  @ApiOperation({
    summary: 'Sayfa IDsi ile üyelikten çıkılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/leave')
  async leavePageMembershipByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.pageMembershipsService.leavePageMembershipByPageId({
      sessionId,

      pageId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üye eklenir',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/add')
  async addPageMembersByPageId(
    @Body() addPageMembersByPageIdDto: AddPageMembersByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.addPageMembersByPageId({
      sessionId,

      sessionPageId,
      pageId,
      ...addPageMembersByPageIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile üyelikten çıkartılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':pageId/remove')
  async removePageMemberByPageId(
    @Body()
    removePageMemberByPageIdDto: RemovePageMemberByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded._id;
    const sessionPageId = res.locals.decoded && res.locals.decoded.p_id ? res.locals.decoded.p_id : null;

    const result = await this.pageMembershipsService.removePageMemberByPageId({
      sessionId,

      sessionPageId,
      pageId,
      ...removePageMemberByPageIdDto,
    });
    return result;
  }
}
