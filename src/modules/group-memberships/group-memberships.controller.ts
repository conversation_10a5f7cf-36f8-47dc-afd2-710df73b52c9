import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { GroupMembershipsService } from './group-memberships.service';
import {
  AddModeratorByGroupIdDto,
  InviteMembersByGroupIdDto,
  RemoveMemberByGroupIdDto,
  RemoveModeratorByGroupIdDto,
  TransferAdministrationByGroupIdDto,
} from './models/group-memberships.dto';
import {
  SearchGroupMembersByGroupIdReturn,
  SearchUsersForGroupCreationReturn,
} from './models/group-memberships.returns';

@ApiTags('groupMemberships')
@Controller('groupMemberships')
export class GroupMembershipsController {
  constructor(private readonly groupMembershipsService: GroupMembershipsService) {}

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundaki birisinin hesabını yönetici yapar',
  })
  @ApiResponse({ status: 200 })
  @Post(':groupId/administration/add')
  async addModeratorByGroupId(
    @Body()
    addModeratorByGroupIdDto: AddModeratorByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.addModeratorByGroupId({
      sessionId,

      groupId,
      ...addModeratorByGroupIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundaki birisini yöneticilerden çıkartır',
  })
  @ApiResponse({ status: 200 })
  @Post(':groupId/administration/remove')
  async removeModeratorByGroupId(
    @Body()
    removeModeratorByGroupIdDto: RemoveModeratorByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.removeModeratorByGroupId({
      sessionId,

      groupId,
      ...removeModeratorByGroupIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundaki birisine yöneticiliği devreder',
  })
  @ApiResponse({ status: 200 })
  @Post(':groupId/administration/transfer')
  async transferAdministrationByGroupId(
    @Body()
    transferAdministrationByGroupIdDto: TransferAdministrationByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.transferAdministrationByGroupId({
      sessionId,

      groupId,
      ...transferAdministrationByGroupIdDto,
    });
    return result;
  }

  // Gruba yeni üye aranırken kullanılır
  @ApiOperation({
    summary: 'Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchUsersForGroupCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('search/:groupId')
  async searchInvitableUsersByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.searchInvitableUsersByGroupId({
      sessionId,

      groupId,
      q,
      limit,
      page,
    });
    return result;
  }

  // Grup oluşturulurken üye aranması için kullanılır
  @ApiOperation({
    summary: 'Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchUsersForGroupCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('search')
  async searchUsersForGroupCreation(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.searchUsersForGroupCreation({
      sessionId,

      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Arkadaş grubundaki üyeleri listeler',
  })
  @ApiResponse({
    status: 200,
    type: SearchGroupMembersByGroupIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get(':groupId/search')
  async searchGroupMembersByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.searchGroupMembersByGroupId({
      sessionId,

      groupId,
      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundan ayrılınır',
  })
  @ApiResponse({ status: 200 })
  @Post(':groupId/leave')
  async leaveGroupByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.leaveGroupByGroupId({
      sessionId,

      groupId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile bir hesaba davet gönderilir',
  })
  @ApiResponse({ status: 200 })
  @Post(':groupId/invite')
  async inviteMembersByGroupId(
    @Body()
    inviteMembersByGroupIdDto: InviteMembersByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.inviteMembersByGroupId({
      sessionId,

      groupId,
      ...inviteMembersByGroupIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile bir hesap gruptan çıkartılır',
  })
  @ApiResponse({ status: 200 })
  @Post(':groupId/remove')
  async removeMemberByGroupId(
    @Body()
    removeMemberByGroupIdDto: RemoveMemberByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.groupMembershipsService.removeMemberByGroupId({
      sessionId,

      groupId,
      ...removeMemberByGroupIdDto,
    });
    return result;
  }
}
