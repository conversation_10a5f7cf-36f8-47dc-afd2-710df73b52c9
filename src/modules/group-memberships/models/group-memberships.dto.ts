import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class AddModeratorByGroupIdDto {
  @ApiProperty({
    description: 'UUID of the user to be promoted to moderator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}

export class InviteMembersByGroupIdDto {
  @ApiProperty({ 
    format: 'uuid',
    description: 'Array of user UUIDs to invite to the group',
    example: ['123e4567-e89b-12d3-a456-************', '987fcdeb-51a2-43d1-9f12-123456789abc'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each user ID must be a valid UUID v4' })
  userIds!: string[];
}

export class RemoveMemberByGroupIdDto {
  @ApiProperty({
    description: 'UUID of the user to be removed from the group',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}

export class RemoveModeratorByGroupIdDto {
  @ApiProperty({
    description: 'UUID of the moderator to be demoted to regular member',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}

export class TransferAdministrationByGroupIdDto {
  @ApiProperty({
    description: 'UUID of the user to transfer group administration to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}
