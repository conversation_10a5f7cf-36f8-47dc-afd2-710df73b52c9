export type AddModeratorByGroupIdParams = {
  sessionId: string;
  groupId: string;
  userId: string;
};

export type InviteMembersByGroupIdParams = {
  sessionId: string;
  groupId: string;
  userIds: string[];
};

export type LeaveGroupByGroupIdParams = {
  sessionId: string;
  groupId: string;
};

export type RemoveMemberByGroupIdParams = {
  sessionId: string;
  groupId: string;
  userId: string;
};

export type RemoveModeratorByGroupIdParams = {
  sessionId: string;
  groupId: string;
  userId: string;
};

export type SearchGroupMembersByGroupIdParams = {
  sessionId: string;
  groupId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchInvitableUsersByGroupIdParams = {
  sessionId: string;
  groupId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchUsersForGroupCreationParams = {
  sessionId: string;
  q: string;
  limit: number;
  page: number;
};

export type TransferAdministrationByGroupIdParams = {
  sessionId: string;
  groupId: string;
  userId: string;
};
