import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, map } from 'rxjs';
import { DataSource } from 'typeorm';
import { GetLatestLocationsParams, GetLocationsParams } from './models/locations.params';
import { GetLatestLocationsReturn, GetLocationsReturn } from './models/locations.returns';
import { PlacesApiResponse } from './models/locations.types';

@Injectable()
export class LocationsService {
  constructor(
    private dataSource: DataSource,
    private configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async getLocations(getLocationsParams: GetLocationsParams): Promise<GetLocationsReturn> {
    const { ...rest } = getLocationsParams;
    return {
      locations: [],
      locationCount: 0,
    };
  }

  async getLatestLocations(getLatestLocationsParams: GetLatestLocationsParams): Promise<GetLatestLocationsReturn> {
    const { ...rest } = getLatestLocationsParams;
    return {
      locations: [],
      locationCount: 0,
    };
  }
}
