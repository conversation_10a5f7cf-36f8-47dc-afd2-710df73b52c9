import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl, IsUUID, Length, Matches } from 'class-validator';

export class CreateCommentDto {
  @ApiProperty({
    description: 'The comment text content',
    example: 'This is a great post!',
    minLength: 1,
    maxLength: 1000,
  })
  @IsString()
  @Length(1, 1000, { message: 'Comment must be between 1 and 1000 characters' })
  comment!: string;

  @ApiProperty({
    description: 'UUID of the vibe being commented on',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'vibeId must be a valid UUID v4' })
  vibeId!: string;

  @ApiProperty({
    description: 'An ivent name can only contain letters, numbers, underscores, and hyphens.',
    example: 'My Awesome Ivent',
    minLength: 3,
    maxLength: 200,
  })
  @IsString()
  @Matches(/^[\p{L}0-9_\-]{3,200}$/u, {
    message: 'Comment can only contain letters, numbers, underscores, and hyphens (3-200 characters)',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Optional thumbnail URL for the comment',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  @IsUrl({}, { message: 'Thumbnail URL must be a valid URL' })
  @IsOptional()
  thumbnailUrl?: string | null;

  @ApiProperty({
    description: 'UUID of the user creating the comment',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'creatorId must be a valid UUID v4' })
  creatorId!: string;
}
