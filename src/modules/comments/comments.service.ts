import { Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { NotificationsService } from '../notifications/notifications.service';
import { CreateCommentParams, DeleteCommentByCommentIdParams } from './models/comments.params';
import { CreateCommentReturn } from './models/comments.returns';

@Injectable()
export class CommentsService {
  constructor(
    private dataSource: DataSource,
    private notificationsService: NotificationsService,
  ) {}

  async createComment(createCommentParams: CreateCommentParams): Promise<CreateCommentReturn> {
    // Initialize
    const { sessionId, vibeId, comment, iventName, thumbnailUrl, creatorId } = createCommentParams;

    const sessionUserResult = await this.dataSource.query(
      `SELECT username, avatar_url FROM users WHERE id = '${sessionId}'`,
    );

    // Create
    const insertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'comments',
        values: {
          vibe_id: vibeId,
          creator_id: sessionId,
          comment,
        },
      }),
    );
    const insertedCommentId = insertResult[0].id;

    // Extra
    await this.notificationsService.sendNotifications(
      {
        notificationType: 'type_34',
        account_type: 'user',
        account_id: sessionId,
        account_name: sessionUserResult[0].avatar_url,
        content_type: 'vibe',
        content_id: vibeId,
        content_name: iventName,
        content_thumbnail: thumbnailUrl,
      },
      [creatorId],
    );

    // Save and return
    return { commentId: insertedCommentId };
  }

  async deleteCommentByCommentId(deleteCommentByCommentIdParams: DeleteCommentByCommentIdParams): Promise<EmptyReturn> {
    const { sessionId, commentId } = deleteCommentByCommentIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('comments')
      .where({
        id: commentId,
        creator_id: sessionId,
      })
      .execute();

    return {};
  }
}
