import { AccountTypeEnum, IventPrivacyEnum } from 'src/entities';
import { CollabDto } from './ivents.dto';

export type CreateIventParams = {
  sessionId: string;
  creatorType: AccountTypeEnum;
  iventName: string;
  thumbnailUrl?: string | null;
  thumbnailBuffer?: string | null;
  dates: string[];
  mapboxId: string;
  latitude: number;
  longitude: number;
  description?: string | null;
  categoryTagId: string;
  tagIds: string[];
  privacy: IventPrivacyEnum;
  allowedUniversityCodes: string[];
  collabs: CollabDto[];
  googleFormsUrl?: string | null;
  instagramUsername?: string | null;
  whatsappUrl?: string | null;
  isWhatsappUrlPrivate?: boolean | null;
  whatsappNumber?: string | null;
  callNumber?: string | null;
  websiteUrl?: string | null;
};

export type DeleteIventByIventIdParams = {
  sessionId: string;
  iventId: string;
};

export type FavoriteIventByIventIdParams = {
  sessionId: string;
  iventId: string;
};

export type GetBannerByIventIdParams = {
  sessionId: string;
  iventIds: string[];
};

export type GetIventPageByIventIdParams = {
  sessionId: string;
  iventId: string;
};

export type GetLatestIventsParams = {
  sessionId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetSuggestedImagesParams = {
  sessionId: string;
  criterias: string[];
};

export type UnfavoriteIventByIventIdParams = {
  sessionId: string;
  iventId: string;
};

export type UpdateDateByIventIdParams = {
  sessionId: string;
  iventId: string;
  newDates: string[];
};

export type UpdateDetailsByIventIdParams = {
  sessionId: string;
  iventId: string;
  newDescription: string;
};

export type UpdateLocationByIventIdParams = {
  sessionId: string;
  iventId: string;
  newlocationId: string;
};

export type GetUpcomingIventParams = {
  sessionId: string;
};
