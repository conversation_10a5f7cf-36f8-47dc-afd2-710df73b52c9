import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Ivent, Location } from 'src/entities';
import { FirebaseModule } from '../firebase/firebase-storage.module';
import { IventsController } from './ivents.controller';
import { IventsService } from './ivents.service';

@Module({
  imports: [FirebaseModule, TypeOrmModule.forFeature([Ivent, Location])],
  providers: [IventsService],
  controllers: [IventsController],
  exports: [],
})
export class IventsModule {}
