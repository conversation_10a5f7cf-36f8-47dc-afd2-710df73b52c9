import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { IventsService } from './ivents.service';
import {
  CreateIventDto,
  GetBannerByIventIdDto,
  UpdateDateByIventIdDto,
  UpdateDetailsByIventIdDto,
  UpdateLocationByIventIdDto,
} from './models/ivents.dto';
import {
  CreateIventReturn,
  GetBannerByIventIdReturn,
  GetIventPageByIventIdReturn,
  GetLatestIventsReturn,
  GetSuggestedImagesReturn,
} from './models/ivents.returns';

@ApiTags('ivents')
@Controller('ivents')
export class IventsController {
  constructor(private readonly iventsService: IventsService) {}

  @ApiOperation({
    summary: 'Ivent oluşturur',
  })
  @ApiResponse({
    status: 200,
    type: CreateIventReturn,
  })
  @Post('create')
  async createIvent(@Body() createIventDto: CreateIventDto, @Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.createIvent({
      sessionId,

      ...createIventDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventi siler',
  })
  @ApiResponse({ status: 200 })
  @Delete(':id/delete')
  async deleteIventByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.deleteIventByIventId({
      sessionId,

      iventId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Kullanıcının katıldığı son iventleri listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetLatestIventsReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'integer' })
  @ApiQuery({ name: 'page', required: false, type: 'integer' })
  @Get('latest')
  async getLatestIvents(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.getLatestIvents({
      sessionId,

      q,
      limit,
      page,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent oluşturulurken resim önerilerini listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetSuggestedImagesReturn,
  })
  @Get('suggestedImages')
  async getSuggestedImages(@Res({ passthrough: true }) res: Response, @Query('criterias') criterias: string) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.getSuggestedImages({
      sessionId,

      criterias: criterias.split(','),
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventin sayfa bilgilerini listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetIventPageByIventIdReturn,
  })
  @Get(':id')
  async getIventPageByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.getIventPageByIventId({
      sessionId,

      iventId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventin küçük kart bilgilerini listeler',
  })
  @ApiResponse({
    status: 200,
    type: GetBannerByIventIdReturn,
  })
  @Post('banner')
  async getBannerByIventId(
    @Body() getBannerByIventIdDto: GetBannerByIventIdDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.getBannerByIventId({
      sessionId,

      ...getBannerByIventIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventin tarih bilgisini günceller',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/date')
  async updateDateByIventId(
    @Body() updateDateByIventIdDto: UpdateDateByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.updateDateByIventId({
      sessionId,

      iventId,
      ...updateDateByIventIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventin detaylarını günceller',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/details')
  async updateDetailsByIventId(
    @Body() updateDetailsByIventIdDto: UpdateDetailsByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.updateDetailsByIventId({
      sessionId,

      iventId,
      ...updateDetailsByIventIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventin konumunu günceller',
  })
  @ApiResponse({ status: 200 })
  @Put(':id/update/location')
  async updateLocationByIventId(
    @Body() updateLocationByIventIdDto: UpdateLocationByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.updateLocationByIventId({
      sessionId,

      iventId,
      ...updateLocationByIventIdDto,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventi favorilere ekler',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/favorite')
  async favoriteIventByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.favoriteIventByIventId({
      sessionId,

      iventId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile iventi favorilerden çıkarır',
  })
  @ApiResponse({ status: 200 })
  @Post(':id/unfavorite')
  async unfavoriteIventByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.unfavoriteIventByIventId({
      sessionId,

      iventId,
    });
    return result;
  }

  @ApiOperation({
    summary: 'Kullanıcının katılacağı en yakın iventi listeler',
  })
  @ApiResponse({ status: 200 })
  @Post('/upcoming')
  async getUpcomingIvent(@Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded._id;

    const result = await this.iventsService.getUpcomingIvent({
      sessionId,
    });
    return result;
  }
}
