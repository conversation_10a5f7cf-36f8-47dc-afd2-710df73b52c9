import type { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1734810000000 implements MigrationInterface {
  name = 'InitialSchema1734810000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable UUID extension
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "user_role_enum" AS ENUM('user', 'admin', 'moderator')
    `);

    await queryRunner.query(`
      CREATE TYPE "user_gender_enum" AS ENUM('male', 'female', 'other', 'prefer_not_to_say')
    `);

    await queryRunner.query(`
      CREATE TYPE "media_format_enum" AS ENUM('image', 'video', 'audio', 'document')
    `);

    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "username" character varying NOT NULL,
        "email" character varying NOT NULL,
        "password_hash" character varying,
        "first_name" character varying,
        "last_name" character varying,
        "bio" text,
        "profile_picture_url" character varying,
        "date_of_birth" date,
        "gender" "user_gender_enum",
        "phone_number" character varying,
        "is_verified" boolean NOT NULL DEFAULT false,
        "role" "user_role_enum" NOT NULL DEFAULT 'user',
        "is_active" boolean NOT NULL DEFAULT true,
        "last_login" TIMESTAMP WITH TIME ZONE,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_users" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_users_username" UNIQUE ("username"),
        CONSTRAINT "UQ_users_email" UNIQUE ("email")
      )
    `);

    // Create locations table
    await queryRunner.query(`
      CREATE TABLE "locations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "location_name" character varying(255) NOT NULL,
        "open_address" text NOT NULL,
        "district" character varying(255),
        "city" character varying,
        "mapbox_id" character varying(255),
        "latitude" decimal(10,8),
        "longitude" decimal(11,8),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_locations" PRIMARY KEY ("id")
      )
    `);

    // Create distributors table
    await queryRunner.query(`
      CREATE TABLE "distributors" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "distributor_name" character varying NOT NULL,
        "thumbnail_url" character varying NOT NULL,
        "website_url" character varying NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_distributors" PRIMARY KEY ("id")
      )
    `);

    // Create hobbies table
    await queryRunner.query(`
      CREATE TABLE "hobbies" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "hobby_name" character varying NOT NULL,
        "level" integer NOT NULL,
        "parent_hobby_id" uuid,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_hobbies" PRIMARY KEY ("id"),
        CONSTRAINT "FK_hobbies_parent" FOREIGN KEY ("parent_hobby_id") REFERENCES "hobbies"("id") ON DELETE SET NULL
      )
    `);

    // Create universities table
    await queryRunner.query(`
      CREATE TABLE "universities" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "university_name" character varying NOT NULL,
        "city" character varying,
        "country" character varying,
        "website_url" character varying,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_universities" PRIMARY KEY ("id")
      )
    `);

    // Create basic indexes
    await queryRunner.query(`CREATE INDEX "IDX_users_email" ON "users" ("email")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_username" ON "users" ("username")`);
    await queryRunner.query(`CREATE INDEX "IDX_locations_lat_lng" ON "locations" ("latitude", "longitude")`);
    await queryRunner.query(`CREATE INDEX "IDX_hobbies_parent" ON "hobbies" ("parent_hobby_id")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_hobbies_parent"`);
    await queryRunner.query(`DROP INDEX "IDX_locations_lat_lng"`);
    await queryRunner.query(`DROP INDEX "IDX_users_username"`);
    await queryRunner.query(`DROP INDEX "IDX_users_email"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "universities"`);
    await queryRunner.query(`DROP TABLE "hobbies"`);
    await queryRunner.query(`DROP TABLE "distributors"`);
    await queryRunner.query(`DROP TABLE "locations"`);
    await queryRunner.query(`DROP TABLE "users"`);

    // Drop enum types
    await queryRunner.query(`DROP TYPE "media_format_enum"`);
    await queryRunner.query(`DROP TYPE "user_gender_enum"`);
    await queryRunner.query(`DROP TYPE "user_role_enum"`);

    // Note: We don't drop UUID extension as it might be used by other applications
  }
}
