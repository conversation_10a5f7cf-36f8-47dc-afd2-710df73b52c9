import { <PERSON>, Get, Res } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

@ApiTags('health')
@Controller('')
export class AppController {
  @ApiOperation({
    summary: 'Health check',
  })
  @Get('health')
  getHealth(@Res({ passthrough: true }) res: Response): string {
    const sessionId = res.locals.decoded._id;

    return `Hello World!, user: ${sessionId}`;
  }
}
