import * as dotenv from 'dotenv';
import { dirname } from 'path';
import { DataSource } from 'typeorm';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: '.env.old' });

// TypeORM DataSource configuration for migrations
export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST!,
  port: parseInt(process.env.POSTGRES_PORT!),
  username: process.env.POSTGRES_USER!,
  password: process.env.POSTGRES_PASSWORD!,
  database: process.env.POSTGRES_DB!,
  ssl: {
    rejectUnauthorized: false, // needed for Aiven
  },
  entities: [process.cwd() + '/src/**/*.entity{.ts,.js}'],
  migrations: [process.cwd() + '/src/migrations/*{.ts,.js}'],
  synchronize: false,
  logging: true,
});
