import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { GroupMembership } from './group-membership.entity';
import { IventGroup } from './ivent-group.entity';
import { User } from './user.entity';

@Entity('groups')
export class Group {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', length: 255 })
  group_name!: string;

  @Column({ type: 'text', nullable: true })
  thumbnail_url!: string | null;

  @Column({ type: 'uuid' })
  creator_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_id' })
  creator?: User;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => GroupMembership, (groupMembership) => groupMembership.group, { cascade: true })
  memberships?: GroupMembership[];

  @OneToMany(() => IventGroup, (iventGroup) => iventGroup.group)
  ivents?: IventGroup[];
}
