import {
  <PERSON><PERSON>n,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Hobby } from './hobby.entity';
import { Ivent } from './ivent.entity';

@Entity('ivent_tags')
export class IventTag {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  ivent_id!: string;

  @ManyToOne(() => Ivent, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @Column({ type: 'uuid' })
  hobby_id!: string;

  @ManyToOne(() => Hobby, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'hobby_id' })
  hobby?: Hobby;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
