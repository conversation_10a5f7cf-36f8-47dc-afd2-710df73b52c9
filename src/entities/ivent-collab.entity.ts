import {
  <PERSON>umn,
  CreateDateC<PERSON>umn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AccountTypeEnum, IventCollabStatusEnum } from './enums';
import { Ivent } from './ivent.entity';
import { Page } from './page.entity';
import { User } from './user.entity';
import { IventUsers } from './views';

@Entity('ivent_collabs')
export class IventCollab {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: IventCollabStatusEnum,
  })
  status!: IventCollabStatusEnum;

  @Column({
    type: 'enum',
    enum: AccountTypeEnum,
  })
  collab_type!: AccountTypeEnum;

  @Column({ type: 'uuid', nullable: true })
  collab_user_id!: string | null;

  @ManyToOne(() => User, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'collab_user_id' })
  collab_user?: User | null;

  @Column({ type: 'uuid', nullable: true })
  collab_page_id!: string | null;

  @ManyToOne(() => Page, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'collab_page_id' })
  collab_page?: Page | null;

  @Column({ type: 'uuid' })
  ivent_id!: string;

  @ManyToOne(() => Ivent, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @Column({ type: 'uuid' })
  inviter_id!: string;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'inviter_id' })
  inviter?: User;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from views
  @OneToMany(() => IventUsers, (iventUsers) => iventUsers.ivent_collab)
  ivent_users?: IventUsers[];
}
