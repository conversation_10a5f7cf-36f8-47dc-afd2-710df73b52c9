import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Vibe } from './vibe.entity';

@Entity('comments')
export class Comment {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'text' })
  comment!: string;

  @Column({ type: 'uuid' })
  vibe_id!: string;

  @ManyToOne(() => Vibe, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'vibe_id' })
  vibe?: Vibe;

  @Column({ type: 'uuid' })
  creator_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_id' })
  creator?: User;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
