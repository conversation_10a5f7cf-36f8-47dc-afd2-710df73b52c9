import {
  <PERSON><PERSON>n,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Hobby } from './hobby.entity';
import { User } from './user.entity';

@Entity('user_hobbies')
export class UserHobby {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  user_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ type: 'uuid' })
  hobby_id!: string;

  @ManyToOne(() => Hobby, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'hobby_id' })
  hobby?: Hobby;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
