import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserRelationshipStatusEnum } from './enums';
import { User } from './user.entity';

@Entity('user_relationships')
export class UserRelationship {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: UserRelationshipStatusEnum,
    default: UserRelationshipStatusEnum.PENDING,
  })
  status!: UserRelationshipStatusEnum;

  @Column({ type: 'uuid' })
  sender_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sender_id' })
  sender?: User;

  @Column({ type: 'uuid' })
  receiver_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'receiver_id' })
  receiver?: User;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
