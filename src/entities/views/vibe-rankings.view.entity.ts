import { stringToNumberTransformer } from 'src/utils/value-transformers';
import { JoinColumn, ManyToOne, OneToOne, ViewColumn, ViewEntity } from 'typeorm';
import { VibeFolder } from '../vibe-folder.entity';
import { Vibe } from '../vibe.entity';

@ViewEntity({
  name: 'vibe_rankings',
  expression: `
    SELECT
        id,
        vibe_folder_id,
        row_number() OVER (
            PARTITION BY
                vibe_folder_id
            ORDER BY created_at
        ) AS vibe_index,
        count(*) OVER (
            PARTITION BY
                vibe_folder_id
        ) AS vibe_count,
        lead(id) OVER (
            PARTITION BY
                vibe_folder_id
            ORDER BY created_at
        ) AS next_vibe_id,
        lag(id) OVER (
            PARTITION BY
                vibe_folder_id
            ORDER BY created_at
        ) AS previous_vibe_id
    FROM vibes
  `,
})
export class VibeRankings {
  @ViewColumn()
  id!: string;

  @OneToOne(() => Vibe)
  @JoinColumn({ name: 'id' })
  vibe?: Vibe;

  @ViewColumn()
  vibe_folder_id!: string;

  @ManyToOne(() => VibeFolder)
  @JoinColumn({ name: 'vibe_folder_id' })
  vibe_folder?: VibeFolder;

  @ViewColumn({ transformer: stringToNumberTransformer })
  vibe_index!: number;

  @ViewColumn({ transformer: stringToNumberTransformer })
  vibe_count!: number;

  @ViewColumn()
  next_vibe_id!: string | null;

  @ViewColumn()
  previous_vibe_id!: string | null;
}
