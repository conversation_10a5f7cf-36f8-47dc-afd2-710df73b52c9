import { stringToArrayTransformer, stringToNumberTransformer } from 'src/utils/value-transformers';
import { JoinColumn, ManyToOne, ViewColumn, ViewEntity } from 'typeorm';
import { Ivent } from '../ivent.entity';
import { User } from '../user.entity';
import { IventUsers } from './ivent-users.view.entity';
import { UserFriendships } from './user-friendships.view.entity';

@ViewEntity({
  name: 'session_friend_summary_of_ivent',
  expression: `
    SELECT
        uf.user_id,
        iu.ivent_id,
        count(u.id) AS friend_count,
        array_to_string(
            (array_agg(u.firstname)) [:2],
            ','
        ) AS friend_first_names,
        array_to_string(
            (array_agg(u.avatar_url)) [:5],
            ','
        ) AS friend_avatar_urls
    FROM
        user_friendships uf
        JOIN ivent_users iu ON iu.account_id = uf.friend_id
        LEFT JOIN users u ON u.id = iu.account_id
    WHERE
        iu.type = 'member'
        AND iu.status IN ('accepted', 'joined')
    GROUP BY
        iu.ivent_id,
        uf.user_id
  `,
  dependsOn: [() => IventUsers, () => UserFriendships],
})
export class SessionFriendSummaryOfIvent {
  @ViewColumn()
  user_id!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @ViewColumn()
  ivent_id!: string;

  @ManyToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @ViewColumn({ transformer: stringToNumberTransformer })
  friend_count!: number;

  @ViewColumn({ transformer: stringToArrayTransformer })
  friend_first_names!: string[];

  @ViewColumn({ transformer: stringToArrayTransformer })
  friend_avatar_urls!: (string | null)[];
}
