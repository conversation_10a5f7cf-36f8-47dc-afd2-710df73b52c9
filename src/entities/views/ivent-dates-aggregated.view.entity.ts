import { stringToArrayTransformer } from 'src/utils/value-transformers';
import { Join<PERSON><PERSON>umn, OneToOne, ViewColumn, ViewEntity } from 'typeorm';
import { Ivent } from '../ivent.entity';

@ViewEntity({
  name: 'ivent_dates_aggregated',
  expression: `
    SELECT string_agg(
            ivent_date::TEXT, ','
            ORDER BY ivent_date
        ) AS date, ivent_id
    FROM ivent_dates
    GROUP BY
        ivent_id
  `,
})
export class IventDatesAggregated {
  @ViewColumn({ transformer: stringToArrayTransformer })
  date!: string[];

  @ViewColumn()
  ivent_id!: string;

  @OneToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;
}
