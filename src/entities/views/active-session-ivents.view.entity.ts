import { Join<PERSON><PERSON>umn, ManyToOne, ViewColumn, ViewEntity } from 'typeorm';
import { IventViewTypeEnum } from '../enums';
import { Ivent } from '../ivent.entity';
import { Page } from '../page.entity';
import { User } from '../user.entity';
import { IventUsers } from './ivent-users.view.entity';

@ViewEntity({
  name: 'active_session_ivents',
  expression: `
    SELECT
        account_id,
        ivent_id,
        CASE
            WHEN type = 'member' THEN 'joined'
            WHEN type IN ('page', 'user') THEN 'created'
        END AS view_type
    FROM ivent_users
    WHERE
        status IN ('accepted', 'joined', 'admin')
  `,
  dependsOn: [() => IventUsers],
})
export class ActiveSessionIvents {
  @ViewColumn()
  account_id!: string;

  @ManyToOne(() => Page, { nullable: true })
  @JoinColumn({ name: 'account_id' })
  page?: Page | null;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'account_id' })
  user?: User | null;

  @ViewColumn()
  ivent_id!: string;

  @ManyToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @ViewColumn()
  view_type!: IventViewTypeEnum;
}
