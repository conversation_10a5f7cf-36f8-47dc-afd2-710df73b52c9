import { stringToArrayTransformer } from 'src/utils/value-transformers';
import { JoinC<PERSON>umn, OneToOne, ViewColumn, ViewEntity } from 'typeorm';
import { Ivent } from '../ivent.entity';

@ViewEntity({
  name: 'ivent_tags_aggregated',
  expression: `
    SELECT string_agg(
            DISTINCT h.hobby_name, ','
        ) AS tags, it.ivent_id
    FROM ivent_tags it
        LEFT JOIN hobbies h ON h.id = it.hobby_id
    GROUP BY
        it.ivent_id
  `,
})
export class IventTagsAggregated {
  @ViewColumn({ transformer: stringToArrayTransformer })
  tags!: string[];

  @ViewColumn()
  ivent_id!: string;

  @OneToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;
}
