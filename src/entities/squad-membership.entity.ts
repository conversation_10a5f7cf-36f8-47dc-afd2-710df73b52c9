import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SquadMembershipStatusEnum } from './enums';
import { Squad } from './squad.entity';
import { User } from './user.entity';
import { IventUsers } from './views';

@Entity('squad_memberships')
export class SquadMembership {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: SquadMembershipStatusEnum,
  })
  status!: SquadMembershipStatusEnum;

  @Column({ type: 'uuid' })
  member_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'member_id' })
  member?: User;

  @Column({ type: 'uuid' })
  squad_id!: string;

  @ManyToOne(() => Squad, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'squad_id' })
  squad?: Squad;

  @Column({ type: 'uuid', nullable: true })
  inviter_id!: string | null;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'inviter_id' })
  inviter?: User | null;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from views
  @OneToMany(() => IventUsers, (iventUsers) => iventUsers.squad_membership)
  ivent_users?: IventUsers[];
}
