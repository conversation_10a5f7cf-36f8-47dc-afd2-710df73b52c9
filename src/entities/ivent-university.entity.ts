import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Ivent } from './ivent.entity';
import { University } from './university.entity';

@Entity('ivent_universities')
export class IventUniversity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  ivent_id!: string;

  @ManyToOne(() => Ivent, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @Column({ type: 'varchar' })
  university_code!: string;

  @ManyToOne(() => University, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'university_code' })
  university?: University;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
