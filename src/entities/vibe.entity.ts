import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Comment } from './comment.entity';
import { AccountTypeEnum, MediaFormatEnum, VibePrivacyEnum } from './enums';
import { Page } from './page.entity';
import { User } from './user.entity';
import { VibeFolder } from './vibe-folder.entity';
import { VibeLike } from './vibe-like.entity';
import { VibeView } from './vibe-view.entity';
import { VibeRankings } from './views';

@Entity('vibes')
export class Vibe {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: MediaFormatEnum,
  })
  media_format!: MediaFormatEnum;

  @Column({ type: 'text', nullable: true })
  thumbnail_url!: string | null;

  @Column({
    type: 'enum',
    enum: AccountTypeEnum,
  })
  creator_type!: AccountTypeEnum;

  @Column({ type: 'text', nullable: true })
  caption!: string | null;

  @Column({ type: 'boolean', default: true })
  is_visible!: boolean;

  @Column({
    type: 'enum',
    enum: VibePrivacyEnum,
    default: VibePrivacyEnum.PUBLIC,
  })
  privacy!: VibePrivacyEnum;

  @Column({ type: 'uuid', nullable: true })
  creator_user_id!: string | null;

  @ManyToOne(() => User, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_user_id' })
  creator_user?: User | null;

  @Column({ type: 'uuid', nullable: true })
  creator_page_id!: string | null;

  @ManyToOne(() => Page, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_page_id' })
  creator_page?: Page | null;

  @Column({ type: 'uuid' })
  vibe_folder_id!: string;

  @ManyToOne(() => VibeFolder, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'vibe_folder_id' })
  vibe_folder?: VibeFolder;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => Comment, (comment) => comment.vibe)
  comments?: Comment[];

  @OneToMany(() => VibeLike, (vibeLike) => vibeLike.vibe)
  likes?: VibeLike[];

  @OneToMany(() => VibeView, (vibeView) => vibeView.vibe)
  views?: VibeView[];

  // Additional relationships from views
  @OneToOne(() => VibeRankings, (vibeRankings) => vibeRankings.vibe)
  vibe_ranking?: VibeRankings;
}
