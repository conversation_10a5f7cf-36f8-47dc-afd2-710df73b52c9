import { stringToNumberTransformer } from 'src/utils/value-transformers';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { IventTag } from './ivent-tag.entity';
import { Ivent } from './ivent.entity';
import { PageTag } from './page-tag.entity';
import { UserHobby } from './user-hobby.entity';

@Entity('hobbies')
export class Hobby {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar' })
  hobby_name!: string;

  @Column({ type: 'integer', transformer: stringToNumberTransformer })
  level!: number;

  @Column({ type: 'uuid', nullable: true })
  parent_hobby_id!: string | null;

  @ManyToOne(() => Hobby, { nullable: true })
  @JoinColumn({ name: 'parent_hobby_id' })
  parent_hobby?: Hobby | null;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => Hobby, (hobby) => hobby.parent_hobby)
  child_hobbies?: Hobby[];

  @OneToMany(() => IventTag, (iventTag) => iventTag.hobby)
  tagged_ivents?: IventTag[];

  @OneToMany(() => Ivent, (ivent) => ivent.category_tag)
  categorized_ivents?: Ivent[];

  @OneToMany(() => PageTag, (pageTag) => pageTag.hobby)
  tagged_pages?: PageTag[];

  @OneToMany(() => UserHobby, (userHobby) => userHobby.hobby)
  users?: UserHobby[];
}
