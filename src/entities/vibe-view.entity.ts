import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Vibe } from './vibe.entity';

@Entity('vibe_views')
export class VibeView {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  user_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ type: 'uuid' })
  vibe_id!: string;

  @ManyToOne(() => Vibe, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'vibe_id' })
  vibe?: Vibe;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
