import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Group } from './group.entity';
import { Ivent } from './ivent.entity';

@Entity('ivent_groups')
export class IventGroup {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  ivent_id!: string;

  @ManyToOne(() => Ivent, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @Column({ type: 'uuid' })
  group_id!: string;

  @ManyToOne(() => Group, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_id' })
  group?: Group;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
