import {
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  Entity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Hobby } from './hobby.entity';
import { Page } from './page.entity';

@Entity('page_tags')
export class PageTag {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  page_id!: string;

  @ManyToOne(() => Page, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'page_id' })
  page?: Page;

  @Column({ type: 'uuid' })
  hobby_id!: string;

  @ManyToOne(() => Hobby, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'hobby_id' })
  hobby?: Hobby;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
