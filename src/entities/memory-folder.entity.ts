import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON>umn,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Ivent } from './ivent.entity';
import { Memory } from './memory.entity';
import { Squad } from './squad.entity';

@Entity('memory_folders')
export class MemoryFolder {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'text', nullable: true })
  thumbnail_url!: string | null;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToOne(() => Ivent, (ivent) => ivent.memory_folder, { nullable: true, onDelete: 'CASCADE' })
  ivent?: Ivent | null;

  @OneToOne(() => Squad, (squad) => squad.memory_folder, { nullable: true, onDelete: 'CASCADE' })
  squad?: Squad | null;

  @OneToMany(() => Memory, (memory) => memory.memory_folder)
  memories?: Memory[];
}
