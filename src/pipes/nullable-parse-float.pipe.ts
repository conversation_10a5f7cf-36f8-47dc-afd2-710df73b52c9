import { ArgumentMetadata, BadRequestException, Injectable, PipeTransform } from '@nestjs/common';

@Injectable()
export class NullableParseFloatPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    const parsed = parseFloat(value);
    if (isNaN(parsed)) {
      throw new BadRequestException(`Validation failed. "${value}" is not a valid float.`);
    }

    return parsed;
  }
}
