import { ArgumentMetadata, BadRequestException, Injectable, PipeTransform } from '@nestjs/common';

@Injectable()
export class NullableParseIntPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
      throw new BadRequestException(`Validation failed. "${value}" is not a valid integer.`);
    }

    return parsed;
  }
}
