import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { JwtPayload, verify } from 'jsonwebtoken';

@Injectable()
export class TokenMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const tokenHeader = req.headers['authorization'];
    const tokenCookie = req.cookies['auth-token'];

    try {
      const token = (tokenHeader && tokenHeader.split(' ')[1]) || tokenCookie || '';
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        throw new Error('JWT_SECRET environment variable is not defined');
      }
      res.locals.decoded = token ? (verify(token, jwtSecret) as JwtPayload) : null;
    } catch (e) {
      console.log('TokenMiddleware error: ', e);
      res.locals.decoded = null;
    } finally {
      next();
    }
  }
}
