import { Injectable, NestMiddleware } from '@nestjs/common';

import { NextFunction, Request, Response } from 'express';

@Injectable()
export class AppLoggerMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const { method, baseUrl, headers, query, body } = req;
    const startTime = Date.now();
    const timestamp = new Date().toISOString();

    if (baseUrl !== '/health' && !baseUrl.startsWith('/mapbox')) {
      console.log(`\n[${timestamp}] 🔹 REQUEST ${method} ${baseUrl}`);
      console.log(`  Headers: ${JSON.stringify(headers)}`);
      console.log(`  Query: ${JSON.stringify(query)}`);
      console.log(`  Body: ${JSON.stringify(body)}`);

      res.on('finish', () => {
        const { statusCode } = res;
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        const responseTimestamp = new Date().toISOString();

        const statusEmoji = statusCode < 400 ? '✅' : '❌';
        console.log(`[${responseTimestamp}] ${statusEmoji} RESPONSE ${method} ${baseUrl}`);
        console.log(`  Status: ${statusCode}`);
        console.log(`  Duration: ${responseTime}ms`);
      });
    } else if (baseUrl.startsWith('/mapbox')) {
      console.log(`[${timestamp}] 🔸 MAPBOX HANDLER`);

      res.on('finish', () => {
        const { statusCode } = res;
        const responseTimestamp = new Date().toISOString();
        console.log(`[${responseTimestamp}] ✅ MAPBOX RESPONSE (${statusCode})`);
      });
    } else {
      console.log(`\n[${timestamp}] 🔹 HEALTH CHECK REQUEST`);

      res.on('finish', () => {
        const { statusCode } = res;
        const responseTimestamp = new Date().toISOString();
        console.log(`[${responseTimestamp}] ✅ HEALTH CHECK RESPONSE (${statusCode})`);
      });
    }

    next();
  }
}
