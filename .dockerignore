# Dependencies
node_modules
npm-debug.log*

# Build outputs
dist
coverage

# Environment files
.env
.env.local
.env.*.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
docs/
exxtra/
postgresSQL/
ivent.dbml

# Test files
test/
*.test.ts
*.spec.ts

# CI/CD
.github/
terraform/
scripts/

# Database backups
db-backups/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore