#!/bin/bash

# Script to set up AWS Systems Manager Parameter Store secrets
# Usage: ./setup-secrets.sh <environment>

# Source utility functions
# 1. Exit immediately if a command fails
# 2. Functions to print colored output
source "$(dirname "$0")/utils.sh"

# Check if environment is provided
if [ -z "$1" ]; then
    print_error "Environment is required. Usage: ./setup-secrets.sh <environment>"
    print_error "Available environments: dev, staging, prod"
    exit 1
fi

ENVIRONMENT=$1

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

print_status "Setting up secrets for environment: $ENVIRONMENT"

# Check if AWS CLI is installed and configured
check_aws

# Function to create or update a parameter
create_parameter() {
    local param_name=$1
    local param_description=$2
    local param_type=${3:-SecureString}
    
    echo -n "Enter value for $param_name: "
    if [ "$param_type" = "SecureString" ]; then
        read -s param_value
        echo
    else
        read param_value
    fi
    
    if [ -z "$param_value" ]; then
        print_warning "Skipping empty value for $param_name"
        return
    fi
    
    # Check if parameter exists
    if aws ssm get-parameter --name "$param_name" --region eu-central-1 >/dev/null 2>&1; then
        print_status "Updating existing parameter: $param_name"
        aws ssm put-parameter \
            --name "$param_name" \
            --value "$param_value" \
            --type "$param_type" \
            --overwrite \
            --region eu-central-1 \
            --description "$param_description" >/dev/null
    else
        print_status "Creating new parameter: $param_name"
        aws ssm put-parameter \
            --name "$param_name" \
            --value "$param_value" \
            --type "$param_type" \
            --region eu-central-1 \
            --description "$param_description" >/dev/null
    fi
    
    print_status "Successfully set parameter: $param_name"
}

# Set up parameters
print_status "Setting up application secrets..."

create_parameter "/ivent/${ENVIRONMENT}/db/password" "Database password for ${ENVIRONMENT} environment"
create_parameter "/ivent/${ENVIRONMENT}/jwt/secret" "JWT secret key for ${ENVIRONMENT} environment"

print_status "Secrets setup completed for environment: $ENVIRONMENT"
print_status "You can now run Terraform deployment with these secrets"
