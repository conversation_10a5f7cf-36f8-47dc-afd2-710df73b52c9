#!/bin/bash

# Run database migrations via ECS task
# Usage: ./run-ecs-migration.sh <environment>

source "$(dirname "$0")/utils.sh"

ENVIRONMENT=${1:-dev}
CLUSTER_NAME="ivent-api-$ENVIRONMENT-cluster"
TASK_DEFINITION="ivent-api-$ENVIRONMENT-migration"

print_status "Running database migration via ECS for environment: $ENVIRONMENT"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

# Check AWS CLI and credentials
check_aws

# Get VPC configuration from existing service
print_status "Retrieving VPC configuration..."
VPC_CONFIG=$(aws ecs describe-services \
  --cluster "$CLUSTER_NAME" \
  --services "ivent-api-$ENVIRONMENT-service" \
  --query 'services[0].networkConfiguration.awsvpcConfiguration' \
  --output json \
  --region eu-central-1 2>/dev/null)

if [ "$VPC_CONFIG" = "null" ] || [ -z "$VPC_CONFIG" ]; then
    print_error "Could not retrieve VPC configuration from existing service"
    print_error "Make sure the ECS service is deployed first"
    exit 1
fi

print_status "VPC configuration retrieved successfully"

# Check if migration task definition exists
if ! aws ecs describe-task-definition \
  --task-definition "$TASK_DEFINITION" \
  --region eu-central-1 >/dev/null 2>&1; then
    print_error "Migration task definition '$TASK_DEFINITION' not found"
    print_error "Please deploy the infrastructure first to create the migration task definition"
    exit 1
fi

# Run migration task
print_status "Starting migration task..."
TASK_ARN=$(aws ecs run-task \
  --cluster "$CLUSTER_NAME" \
  --task-definition "$TASK_DEFINITION" \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration=$VPC_CONFIG" \
  --query 'tasks[0].taskArn' \
  --output text \
  --region eu-central-1)

if [ "$TASK_ARN" = "None" ] || [ -z "$TASK_ARN" ]; then
    print_error "Failed to start migration task"
    exit 1
fi

print_status "Migration task started: $TASK_ARN"

# Extract task ID for easier reference
TASK_ID=$(echo "$TASK_ARN" | cut -d'/' -f3)
print_status "Task ID: $TASK_ID"

# Wait for task completion with timeout
print_status "Waiting for migration task to complete (timeout: 10 minutes)..."
if timeout 600 aws ecs wait tasks-stopped \
  --cluster "$CLUSTER_NAME" \
  --tasks "$TASK_ARN" \
  --region eu-central-1; then
    print_status "Migration task completed"
else
    print_error "Migration task timed out or failed to complete"
    print_status "Task ARN: $TASK_ARN"
    exit 1
fi

# Check task exit code
print_status "Checking migration task result..."
TASK_DETAILS=$(aws ecs describe-tasks \
  --cluster "$CLUSTER_NAME" \
  --tasks "$TASK_ARN" \
  --query 'tasks[0]' \
  --output json \
  --region eu-central-1)

EXIT_CODE=$(echo "$TASK_DETAILS" | jq -r '.containers[0].exitCode // "null"')
LAST_STATUS=$(echo "$TASK_DETAILS" | jq -r '.lastStatus')

print_status "Task Status: $LAST_STATUS"
print_status "Exit Code: $EXIT_CODE"

if [ "$EXIT_CODE" = "0" ]; then
    print_status "✅ Database migration completed successfully!"
else
    print_error "❌ Database migration failed with exit code: $EXIT_CODE"
    
    # Show recent logs for debugging
    print_status "Recent migration task logs:"
    aws logs tail "/ecs/ivent-api-$ENVIRONMENT-migration" \
      --since 10m \
      --region eu-central-1 \
      --follow --max-items 50 || print_warning "Could not retrieve logs"
    
    exit 1
fi

print_status "Migration process completed successfully!"
print_status "You can view full logs with:"
print_status "aws logs tail /ecs/ivent-api-$ENVIRONMENT-migration --region eu-central-1"
