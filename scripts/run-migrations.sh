#!/bin/bash

# Database migration runner for AWS ECS
# Usage: ./run-migrations.sh <environment>

source "$(dirname "$0")/utils.sh"

ENVIRONMENT=${1:-dev}

print_status "Starting database migrations for environment: $ENVIRONMENT"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

# Check dependencies
check_aws

# Set environment variables from SSM
print_status "Retrieving database configuration from AWS SSM..."

export POSTGRES_HOST=$(aws ssm get-parameter --name "/ivent/$ENVIRONMENT/db/host" --query 'Parameter.Value' --output text --region eu-central-1 2>/dev/null || echo "")
export POSTGRES_PORT=5432
export POSTGRES_USER=ivent_admin
export POSTGRES_DB="ivent_api_$ENVIRONMENT"
export POSTGRES_PASSWORD=$(aws ssm get-parameter --name "/ivent/$ENVIRONMENT/db/password" --with-decryption --query 'Parameter.Value' --output text --region eu-central-1 2>/dev/null || echo "")

# If SSM parameters don't exist, try to get from Terraform output
if [ -z "$POSTGRES_HOST" ]; then
    print_status "SSM parameter not found, trying Terraform output..."
    if [ -d "terraform" ]; then
        cd terraform
        export POSTGRES_HOST=$(terraform output -raw database_endpoint 2>/dev/null | cut -d':' -f1 || echo "")
        cd ..
    fi
fi

if [ -z "$POSTGRES_HOST" ] || [ -z "$POSTGRES_PASSWORD" ]; then
    print_error "Could not retrieve database configuration. Please ensure:"
    print_error "1. SSM parameters exist: /ivent/$ENVIRONMENT/db/host and /ivent/$ENVIRONMENT/db/password"
    print_error "2. Or run from terraform directory with valid state"
    exit 1
fi

print_status "Database connection configured for: $POSTGRES_HOST"

# Test database connectivity
print_status "Testing database connectivity..."
if command -v pg_isready >/dev/null 2>&1; then
    if ! pg_isready -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB"; then
        print_warning "pg_isready check failed, but continuing with migration attempt..."
    fi
else
    print_warning "pg_isready not available, skipping connectivity test..."
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
fi

# Show current migration status
print_status "Current migration status:"
npm run migration:show || print_warning "Could not show migration status (this is normal for first run)"

# Run migrations
print_status "Running database migrations..."
if npm run migration:run; then
    print_status "✅ Database migrations completed successfully!"
else
    print_error "❌ Database migrations failed!"
    print_error "Check the error messages above for details."
    exit 1
fi

# Show final migration status
print_status "Final migration status:"
npm run migration:show

print_status "Migration process completed for environment: $ENVIRONMENT"
