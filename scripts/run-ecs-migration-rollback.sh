#!/bin/bash

# Rollback database migrations via ECS task
# Usage: ./run-ecs-migration-rollback.sh <environment>

source "$(dirname "$0")/utils.sh"

ENVIRONMENT=${1:-dev}
CLUSTER_NAME="ivent-api-$ENVIRONMENT-cluster"
TASK_DEFINITION="ivent-api-$ENVIRONMENT-migration"

print_warning "⚠️  DANGER: Rolling back database migration!"
print_warning "This will revert the last applied migration for environment: $ENVIRONMENT"
print_warning "This action cannot be undone automatically."

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

# Extra confirmation for production
if [ "$ENVIRONMENT" = "prod" ]; then
    print_error "🚨 PRODUCTION ROLLBACK DETECTED 🚨"
    print_error "You are about to rollback a migration in PRODUCTION!"
    print_error "This is extremely dangerous and should only be done in emergencies."
    echo ""
    read -p "Type 'ROLLBACK PRODUCTION' to confirm: " confirm
    if [ "$confirm" != "ROLLBACK PRODUCTION" ]; then
        print_status "Production rollback cancelled."
        exit 0
    fi
else
    read -p "Are you sure you want to rollback the migration? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        print_status "Migration rollback cancelled."
        exit 0
    fi
fi

# Check AWS CLI and credentials
check_aws

# Get VPC configuration from existing service
print_status "Retrieving VPC configuration..."
VPC_CONFIG=$(aws ecs describe-services \
  --cluster "$CLUSTER_NAME" \
  --services "ivent-api-$ENVIRONMENT-service" \
  --query 'services[0].networkConfiguration.awsvpcConfiguration' \
  --output json \
  --region eu-central-1 2>/dev/null)

if [ "$VPC_CONFIG" = "null" ] || [ -z "$VPC_CONFIG" ]; then
    print_error "Could not retrieve VPC configuration from existing service"
    exit 1
fi

# Run rollback task with revert command
print_status "Starting migration rollback task..."
TASK_ARN=$(aws ecs run-task \
  --cluster "$CLUSTER_NAME" \
  --task-definition "$TASK_DEFINITION" \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration=$VPC_CONFIG" \
  --overrides '{
    "containerOverrides": [{
      "name": "migration",
      "command": ["/bin/sh", "-c", "npm run migration:revert"]
    }]
  }' \
  --query 'tasks[0].taskArn' \
  --output text \
  --region eu-central-1)

if [ "$TASK_ARN" = "None" ] || [ -z "$TASK_ARN" ]; then
    print_error "Failed to start rollback task"
    exit 1
fi

print_status "Rollback task started: $TASK_ARN"

# Wait for task completion
print_status "Waiting for rollback task to complete..."
if timeout 300 aws ecs wait tasks-stopped \
  --cluster "$CLUSTER_NAME" \
  --tasks "$TASK_ARN" \
  --region eu-central-1; then
    print_status "Rollback task completed"
else
    print_error "Rollback task timed out"
    exit 1
fi

# Check task exit code
EXIT_CODE=$(aws ecs describe-tasks \
  --cluster "$CLUSTER_NAME" \
  --tasks "$TASK_ARN" \
  --query 'tasks[0].containers[0].exitCode' \
  --output text \
  --region eu-central-1)

if [ "$EXIT_CODE" = "0" ]; then
    print_status "✅ Database migration rollback completed successfully!"
else
    print_error "❌ Database migration rollback failed with exit code: $EXIT_CODE"
    
    # Show logs for debugging
    print_status "Rollback task logs:"
    aws logs tail "/ecs/ivent-api-$ENVIRONMENT-migration" \
      --since 5m \
      --region eu-central-1 || print_warning "Could not retrieve logs"
    
    exit 1
fi

print_warning "⚠️  Migration rollback completed!"
print_warning "⚠️  Your application may need to be rolled back to a compatible version!"
print_status "Monitor your application logs and health checks carefully."
