#!/bin/bash

# Generate new migration based on entity changes
# Usage: ./generate-migration.sh <migration-name>

source "$(dirname "$0")/utils.sh"

if [ -z "$1" ]; then
    print_error "Migration name is required."
    print_error "Usage: ./generate-migration.sh <migration-name>"
    print_error "Example: ./generate-migration.sh AddUserProfileFields"
    exit 1
fi

MIGRATION_NAME=$1

print_status "Generating migration: $MIGRATION_NAME"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
fi

# Ensure local database is running for comparison
print_status "Checking local development database..."
if command -v docker-compose >/dev/null 2>&1; then
    if ! docker-compose ps postgres | grep -q "Up"; then
        print_status "Starting local PostgreSQL..."
        docker-compose up -d postgres
        
        # Wait for database to be ready
        print_status "Waiting for database to be ready..."
        sleep 10
        
        # Check if database is ready
        for i in {1..30}; do
            if docker-compose exec postgres pg_isready -U ivent_admin -d ivent_db >/dev/null 2>&1; then
                print_status "Database is ready!"
                break
            fi
            if [ $i -eq 30 ]; then
                print_error "Database failed to start after 30 attempts"
                exit 1
            fi
            sleep 2
        done
    fi
else
    print_warning "Docker Compose not available. Make sure your local database is running."
fi

# Set local environment variables
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_USER=ivent_admin
export POSTGRES_PASSWORD=QDWez3jJVE4Gpmu8
export POSTGRES_DB=ivent_db

# Generate migration
print_status "Generating migration file..."
if npm run migration:generate -- "src/migrations/$MIGRATION_NAME"; then
    print_status "✅ Migration generated successfully!"
    
    # Show the generated file
    LATEST_MIGRATION=$(ls -t src/migrations/*.ts 2>/dev/null | head -n1)
    if [ -n "$LATEST_MIGRATION" ]; then
        print_status "Generated migration file: $LATEST_MIGRATION"
        print_status "Please review the migration before committing:"
        echo ""
        head -20 "$LATEST_MIGRATION"
        echo ""
        print_warning "⚠️  Important: Review the generated migration carefully!"
        print_warning "⚠️  Test the migration locally before committing!"
    fi
else
    print_error "❌ Migration generation failed!"
    print_error "This could mean:"
    print_error "1. No changes detected in entities"
    print_error "2. Database connection issues"
    print_error "3. TypeORM configuration problems"
    exit 1
fi

print_status "Next steps:"
print_status "1. Review the generated migration file"
print_status "2. Test the migration: npm run migration:run"
print_status "3. Test rollback: npm run migration:revert"
print_status "4. Commit the migration: git add src/migrations/ && git commit"
