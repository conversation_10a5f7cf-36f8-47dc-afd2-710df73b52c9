#!/bin/bash

# Comprehensive deployment testing script
# Usage: ./test-deployment.sh <environment> [load-balancer-dns]

# Source utility functions
# 1. Exit immediately if a command fails
# 2. Functions to print colored output
source "$(dirname "$0")/utils.sh"

# Check if environment is provided
if [ -z "$1" ]; then
    print_error "Environment is required. Usage: ./test-deployment.sh <environment> [load-balancer-dns]"
    print_error "Available environments: dev, staging, prod"
    exit 1
fi

ENVIRONMENT=$1
ALB_DNS=$2

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment. Must be one of: dev, staging, prod"
    exit 1
fi

print_status "Starting deployment tests for environment: $ENVIRONMENT"

# Get ALB DNS from Terraform if not provided
if [ -z "$ALB_DNS" ]; then
    if [ -d "terraform" ]; then
        cd terraform
        ALB_DNS=$(terraform output -raw load_balancer_dns_name 2>/dev/null || echo "")
        cd ..
    fi
    
    if [ -z "$ALB_DNS" ]; then
        print_error "Load balancer DNS not found. Please provide it as second argument."
        exit 1
    fi
fi

print_status "Testing deployment at: http://$ALB_DNS"

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_test "Running: $test_name"
    
    if eval "$test_command"; then
        if [ -n "$expected_result" ]; then
            # Additional validation if expected result is provided
            if eval "$expected_result"; then
                echo -e "  ${GREEN}✓ PASSED${NC}"
                TESTS_PASSED=$((TESTS_PASSED + 1))
            else
                echo -e "  ${RED}✗ FAILED${NC} (validation failed)"
                TESTS_FAILED=$((TESTS_FAILED + 1))
            fi
        else
            echo -e "  ${GREEN}✓ PASSED${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        fi
    else
        echo -e "  ${RED}✗ FAILED${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    echo
}

# Wait for service to be ready
print_status "Waiting for service to be ready..."
sleep 30

# Test 1: Health Check
run_test "Health Check Endpoint" \
    "curl -f -s http://$ALB_DNS/health > /dev/null" \
    ""

# Test 2: Health Check Response Content
run_test "Health Check Response Content" \
    "response=\$(curl -s http://$ALB_DNS/health); echo \"Response: \$response\"" \
    "[[ \$response == *\"Hello World\"* ]]"

# Test 3: API Documentation (only for non-prod)
if [ "$ENVIRONMENT" != "prod" ]; then
    run_test "API Documentation Endpoint" \
        "curl -f -s http://$ALB_DNS/api > /dev/null" \
        ""
    
    run_test "Swagger UI Content" \
        "response=\$(curl -s http://$ALB_DNS/api); echo \"Swagger response received\"" \
        "[[ \$response == *\"swagger\"* ]] || [[ \$response == *\"Swagger\"* ]]"
fi

# Test 4: API JSON Schema
if [ "$ENVIRONMENT" != "prod" ]; then
    run_test "API JSON Schema" \
        "curl -f -s http://$ALB_DNS/api-json > /dev/null" \
        ""
fi

# Test 5: Response Time Test
run_test "Response Time Test (< 2 seconds)" \
    "response_time=\$(curl -o /dev/null -s -w '%{time_total}' http://$ALB_DNS/health); echo \"Response time: \${response_time}s\"" \
    "[[ \$(echo \"\$response_time < 2\" | bc -l) -eq 1 ]]"

# Test 6: Load Balancer Health
run_test "Load Balancer Target Health" \
    "aws elbv2 describe-target-health --target-group-arn \$(aws elbv2 describe-target-groups --names \"ivent-api-$ENVIRONMENT-tg\" --query 'TargetGroups[0].TargetGroupArn' --output text) --query 'TargetHealthDescriptions[0].TargetHealth.State' --output text | grep -q 'healthy'" \
    ""

# Test 7: ECS Service Status
run_test "ECS Service Running Status" \
    "aws ecs describe-services --cluster \"ivent-api-$ENVIRONMENT-cluster\" --services \"ivent-api-$ENVIRONMENT-service\" --query 'services[0].status' --output text | grep -q 'ACTIVE'" \
    ""

# Test 8: ECS Service Desired vs Running Count
run_test "ECS Service Task Count" \
    "desired=\$(aws ecs describe-services --cluster \"ivent-api-$ENVIRONMENT-cluster\" --services \"ivent-api-$ENVIRONMENT-service\" --query 'services[0].desiredCount' --output text); running=\$(aws ecs describe-services --cluster \"ivent-api-$ENVIRONMENT-cluster\" --services \"ivent-api-$ENVIRONMENT-service\" --query 'services[0].runningCount' --output text); echo \"Desired: \$desired, Running: \$running\"" \
    "[[ \$desired -eq \$running ]]"

# Test 9: Database Connectivity (indirect test via health check)
run_test "Database Connectivity Test" \
    "curl -f -s http://$ALB_DNS/health > /dev/null" \
    ""

# Test 10: SSL/HTTPS Test (if configured)
# Note: This assumes HTTPS is configured. Skip if not.
# run_test "HTTPS Endpoint Test" \
#     "curl -f -s https://$ALB_DNS/health > /dev/null" \
#     ""

# Test 11: Security Headers Test
run_test "Security Headers Check" \
    "headers=\$(curl -I -s http://$ALB_DNS/health); echo \"Headers received\"" \
    ""

# Test 12: CORS Headers Test
run_test "CORS Headers Test" \
    "curl -H \"Origin: http://example.com\" -H \"Access-Control-Request-Method: GET\" -H \"Access-Control-Request-Headers: X-Requested-With\" -X OPTIONS -s http://$ALB_DNS/health > /dev/null" \
    ""

# Test 13: CloudWatch Logs Test
run_test "CloudWatch Logs Availability" \
    "aws logs describe-log-groups --log-group-name-prefix \"/ecs/ivent-api-$ENVIRONMENT\" --query 'logGroups[0].logGroupName' --output text | grep -q \"ivent-api-$ENVIRONMENT\"" \
    ""

# Test 14: S3 Bucket Accessibility
run_test "S3 Bucket Accessibility" \
    "bucket_name=\$(aws s3 ls | grep \"ivent-api-$ENVIRONMENT-media\" | awk '{print \$3}' | head -1); aws s3 ls \"s3://\$bucket_name\" > /dev/null" \
    ""

# Test 15: Parameter Store Secrets
run_test "Parameter Store Secrets Accessibility" \
    "aws ssm get-parameter --name \"/ivent/$ENVIRONMENT/db/password\" --with-decryption --query 'Parameter.Value' --output text > /dev/null" \
    ""

# Load Testing (optional, only if artillery is installed)
if command -v artillery >/dev/null 2>&1; then
    print_test "Running Load Test (10 concurrent users, 50 requests each)"
    if artillery quick --count 10 --num 50 "http://$ALB_DNS/health" > /dev/null 2>&1; then
        echo -e "  ${GREEN}✓ PASSED${NC} - Load test completed successfully"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "  ${RED}✗ FAILED${NC} - Load test failed"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo
else
    print_warning "Artillery not installed. Skipping load test. Install with: npm install -g artillery"
fi

# Summary
echo "=================================="
echo "DEPLOYMENT TEST SUMMARY"
echo "=================================="
echo "Environment: $ENVIRONMENT"
echo "Load Balancer: http://$ALB_DNS"
echo "Total Tests: $TOTAL_TESTS"
echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Failed: ${RED}$TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! Deployment is healthy.${NC}"
    
    # Print useful URLs
    echo -e "\n${BLUE}Useful URLs:${NC}"
    echo "Application: http://$ALB_DNS"
    echo "Health Check: http://$ALB_DNS/health"
    if [ "$ENVIRONMENT" != "prod" ]; then
        echo "API Documentation: http://$ALB_DNS/api"
        echo "API JSON: http://$ALB_DNS/api-json"
    fi
    
    exit 0
else
    echo -e "\n${RED}❌ $TESTS_FAILED TESTS FAILED! Please check the deployment.${NC}"
    
    # Print troubleshooting tips
    echo -e "\n${YELLOW}Troubleshooting Tips:${NC}"
    echo "1. Check ECS service logs: aws logs tail /ecs/ivent-api-$ENVIRONMENT --follow"
    echo "2. Check ECS service status: aws ecs describe-services --cluster ivent-api-$ENVIRONMENT-cluster --services ivent-api-$ENVIRONMENT-service"
    echo "3. Check target group health: aws elbv2 describe-target-health --target-group-arn <target-group-arn>"
    echo "4. Check CloudWatch alarms: aws cloudwatch describe-alarms --alarm-names ivent-api-$ENVIRONMENT-*"
    
    exit 1
fi
