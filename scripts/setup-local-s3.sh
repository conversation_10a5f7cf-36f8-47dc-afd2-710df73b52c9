#!/bin/bash

# Script to set up local S3 buckets using LocalStack
# Usage: ./setup-local-s3.sh

# Source utility functions
# 1. Exit immediately if a command fails
# 2. Functions to print colored output
source "$(dirname "$0")/utils.sh"

# Check if LocalStack is running
if ! curl -s http://localhost:4566/_localstack/health >/dev/null 2>&1; then
    print_error "LocalStack is not running. Please start it with 'docker-compose up localstack'"
    exit 1
fi

print_status "LocalStack is running. Setting up S3 buckets..."

# Set AWS CLI to use LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=eu-central-1

BUCKET_NAME=${AWS_S3_BUCKET_NAME:-ivent-media-dev}

# Create the main media bucket
print_status "Creating S3 bucket: $BUCKET_NAME"
aws --endpoint-url=http://localhost:4566 s3 mb "s3://$BUCKET_NAME" || print_warning "Bucket may already exist"

# Create folder structure
print_status "Creating folder structure in S3 bucket..."
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "vibes/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "thumbnails/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "thumbnails/vibes/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "ivents/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "memories/" --content-length 0

# Set CORS configuration for the bucket
print_status "Setting CORS configuration..."
cat > /tmp/cors-config.json << EOF
{
    "CORSRules": [
        {
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
            "AllowedOrigins": ["*"],
            "ExposeHeaders": ["ETag"],
            "MaxAgeSeconds": 3000
        }
    ]
}
EOF

aws --endpoint-url=http://localhost:4566 s3api put-bucket-cors --bucket "$BUCKET_NAME" --cors-configuration file:///tmp/cors-config.json

# List buckets to verify
print_status "Verifying bucket creation..."
aws --endpoint-url=http://localhost:4566 s3 ls

print_status "Local S3 setup completed successfully!"
print_status "Bucket: $BUCKET_NAME"
print_status "Endpoint: http://localhost:4566"

# Clean up
rm -f /tmp/cors-config.json
