version: "3.9"

services:
  # Application for production (without database - using RDS)
  backend:
    container_name: ivent_api_prod
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    environment:
      NODE_ENV: production
      # These will be set by ECS task definition in AWS
      # POSTGRES_HOST: ${POSTGRES_HOST}
      # POSTGRES_PORT: ${POSTGRES_PORT}
      # POSTGRES_DB: ${POSTGRES_DB}
      # POSTGRES_USER: ${POSTGRES_USER}
      # AWS_REGION: ${AWS_REGION}
      # AWS_S3_BUCKET_NAME: ${AWS_S3_BUCKET_NAME}
    ports:
      - "3000:3000"
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
